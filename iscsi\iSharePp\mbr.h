#pragma once
#ifndef SECSIZE
#define SECSIZE (0x200)
#endif
#ifndef MBRSIGN
#define MBRSIGN (0xAA55)
#endif
#ifndef DBR_SIZE
#define DBR_SIZE (512)
#endif
#ifdef OEM_HAPPYSHARE_UPDATE
//为了单独对游戏更新机设置盘符而确定的安全位置
#define CLIENTLETTER_OFFSET (DBR_SIZE-68)
#else
#define CLIENTLETTER_OFFSET (DBR_SIZE-3)
#endif
#ifndef HIDN_DISK_LETTER
#define HIDN_DISK_LETTER (0xFF)
#define HIDN_DISK_LETTER1 (-1)
#define HIDN_DISK_DISKLESS_LETTER (0xFE)
#define HIDN_DISK_LETTER2 (-2)
#define HIDN_DISK_VHDBOOT_LETTER (0xFD)
#define HIDN_DISK_LETTER3 (-3)
#endif
#ifndef SYSTEMLETTER_CHAR
#define SYSTEMLETTER_CHAR 'C'
#endif
#ifndef LASTLETTER_CHAR
#define LASTLETTER_CHAR 'Z'
#endif

inline bool IsGoodDiskletter(char dosletter) {
	return (dosletter > SYSTEMLETTER_CHAR && dosletter <= LASTLETTER_CHAR) ? true : false;
};
inline bool IsHidedDiskletter(char dosletter) {
	return (dosletter == HIDN_DISK_LETTER1 || dosletter == HIDN_DISK_LETTER2 || dosletter == HIDN_DISK_LETTER3) ? true : false;
};

inline bool IsGoodDiskletterSet(char dosletter) {
	return (IsGoodDiskletter(dosletter) || IsHidedDiskletter(dosletter)) ? true : false;
};

#pragma pack( 1 )

typedef struct _PARTITION_DESCRIPTOR {
	UCHAR ActiveFlag;               // Bootable or not
	UCHAR StartingTrack;            // Not used
	UCHAR StartingCylinderLsb;      // Not used
	UCHAR StartingCylinderMsb;      // Not used
	UCHAR PartitionType;            // 12 bit FAT, 16 bit FAT etc.
	UCHAR EndingTrack;              // Not used
	UCHAR EndingCylinderLsb;        // Not used
	UCHAR EndingCylinderMsb;        // Not used
	UCHAR StartingSectorLsb0;       // Hidden sectors
	UCHAR StartingSectorLsb1;
	UCHAR StartingSectorMsb0;
	UCHAR StartingSectorMsb1;
	UCHAR PartitionLengthLsb0;      // Sectors in this partition
	UCHAR PartitionLengthLsb1;
	UCHAR PartitionLengthMsb0;
	UCHAR PartitionLengthMsb1;
} PARTITION_DESCRIPTOR, * PPARTITION_DESCRIPTOR;

//
// Number of partition table entries
//

#define NUM_PARTITION_TABLE_ENTRIES     4

//
// Boot record signature value.
//

#define BOOT_RECORD_SIGNATURE          MBRSIGN

//
// Partition active flag - i.e., boot indicator
//

#define PARTITION_ACTIVE_FLAG          0x80

#ifndef PACKED_BOOT_SECTOR_MBR_DEFINED
#define PACKED_BOOT_SECTOR_MBR_DEFINED
typedef struct _PACKED_BOOT_SECTOR_MBR
{
	UCHAR	BootStrap[0x1B8];//启动代码
	ULONG   dwDiskMark;		//磁盘签名
	UCHAR   dwReserved[2];	//保留
	PARTITION_DESCRIPTOR	bsPartEnts[NUM_PARTITION_TABLE_ENTRIES];
	USHORT	bsSignature;	// boot block signature (0xAA55h)
}PACKED_BOOT_SECTOR_MBR, * PPACKED_BOOT_SECTOR_MBR;
#endif // PACKED_BOOT_SECTOR_MBR_DEFINED

typedef struct tag_PtItem
{
	BYTE	bState;
	BYTE	bStartHead;
	BYTE	bStartSec;
	BYTE	bStartCyl;
	BYTE	bType;
	BYTE	bEndHead;
	BYTE	bEndSec;
	BYTE	bEndCyl;
	DWORD	dwStartSec;
	DWORD	dwTotalSecs;
}PTITEM, * LPPTITEM;

typedef struct _mbr
{
	BYTE	bootcode[0x1BE];
	PTITEM	bsPartEnts[4];
	WORD	bsSignature;			// boot block signature (0xAA55h)
}MBR, * PMBR;

#pragma pack()
