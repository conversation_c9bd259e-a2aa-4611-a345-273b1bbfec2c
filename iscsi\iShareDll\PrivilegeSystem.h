﻿#pragma once
#include <string>
//测试当前进程是否有某个权限
BOOL CheckPrivilege(LPCTSTR Privilege);
//将进程设置为调试权限
BOOL SetPrivilege(LPCTSTR Privilege, BOOL bEnablePrivilege);
// BOOL EnablePrivilege( LPCTSTR Privilege );
// BOOL IsAdministrator();
BOOL SetProcessWorkingSet(SIZE_T minsize, SIZE_T maxsize);//设置程序的可用内存
inline BOOL AdjustPrivilegeForJunctionPoint(BOOL bRead) {
	return SetPrivilege(bRead ? SE_RESTORE_NAME : SE_BACKUP_NAME, TRUE);
};
//打开注册表装载和还原的权限
inline BOOL SetRegisterPrivilege() {
	return 	SetPrivilege(SE_RESTORE_NAME, TRUE) && SetPrivilege(SE_BACKUP_NAME, TRUE);
}
//读取固件的权利 https://docs.microsoft.com/zh-cn/windows/win32/api/winbase/nf-winbase-getfirmwareenvironmentvariablew
inline BOOL ReadFirwmarePrivilege() {
	return 	SetPrivilege(SE_SYSTEM_ENVIRONMENT_NAME, TRUE);
}
//设置文件的有效数据权限 https://learn.microsoft.com/zh-cn/windows/win32/api/fileapi/nf-fileapi-setfilevaliddata
inline BOOL SetFileValidDataPrivilege() {
	return 	SetPrivilege(SE_MANAGE_VOLUME_NAME, TRUE);
}

std::wstring GetCurrentUserSid();