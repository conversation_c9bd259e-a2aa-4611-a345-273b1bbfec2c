<?xml version="1.0" encoding = "Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.00"
	Name="zlibvc"
	SccProjectName=""
	SccLocalPath="">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory=".\DebugDll"
			IntermediateDirectory=".\DebugDll"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="WIN32,ZLIB_WINAPI,ASMV,ASMINF"
				ExceptionHandling="FALSE"
				RuntimeLibrary="1"
				PrecompiledHeaderFile=".\DebugDll/zlibvc.pch"
				AssemblerListingLocation=".\DebugDll/"
				ObjectFile=".\DebugDll/"
				ProgramDataBaseFileName=".\DebugDll/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions="/MACHINE:I386"
				AdditionalDependencies="gvmat32.obj inffas32.obj"
				OutputFile=".\DebugDll\zlibwapi.dll"
				LinkIncremental="2"
				SuppressStartupBanner="TRUE"
				ModuleDefinitionFile=".\zlibvc.def"
				GenerateDebugInformation="TRUE"
				ProgramDatabaseFile=".\DebugDll/zlibwapi.pdb"
				SubSystem="2"
				ImportLibrary=".\DebugDll/zlibwapi.lib"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\DebugDll/zlibvc.tlb"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1036"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
		</Configuration>
		<Configuration
			Name="ReleaseWithoutAsm|Win32"
			OutputDirectory=".\zlibDllWithoutAsm"
			IntermediateDirectory=".\zlibDllWithoutAsm"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="WIN32,ZLIB_WINAPI"
				StringPooling="TRUE"
				ExceptionHandling="FALSE"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="TRUE"
				PrecompiledHeaderFile=".\zlibDllWithoutAsm/zlibvc.pch"
				AssemblerOutput="2"
				AssemblerListingLocation=".\zlibDllWithoutAsm/"
				ObjectFile=".\zlibDllWithoutAsm/"
				ProgramDataBaseFileName=".\zlibDllWithoutAsm/"
				BrowseInformation="1"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions="/MACHINE:I386"
				AdditionalDependencies="crtdll.lib"
				OutputFile=".\zlibDllWithoutAsm\zlibwapi.dll"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				IgnoreAllDefaultLibraries="TRUE"
				ModuleDefinitionFile=".\zlibvc.def"
				ProgramDatabaseFile=".\zlibDllWithoutAsm/zlibwapi.pdb"
				GenerateMapFile="TRUE"
				MapFileName=".\zlibDllWithoutAsm/zlibwapi.map"
				SubSystem="2"
				OptimizeForWindows98="1"
				ImportLibrary=".\zlibDllWithoutAsm/zlibwapi.lib"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\zlibDllWithoutAsm/zlibvc.tlb"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1036"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
		</Configuration>
		<Configuration
			Name="ReleaseWithoutCrtdll|Win32"
			OutputDirectory=".\zlibDllWithoutCrtDll"
			IntermediateDirectory=".\zlibDllWithoutCrtDll"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="WIN32,ZLIB_WINAPI,ASMV,ASMINF"
				StringPooling="TRUE"
				ExceptionHandling="FALSE"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="TRUE"
				PrecompiledHeaderFile=".\zlibDllWithoutCrtDll/zlibvc.pch"
				AssemblerOutput="2"
				AssemblerListingLocation=".\zlibDllWithoutCrtDll/"
				ObjectFile=".\zlibDllWithoutCrtDll/"
				ProgramDataBaseFileName=".\zlibDllWithoutCrtDll/"
				BrowseInformation="1"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions="/MACHINE:I386"
				AdditionalDependencies="gvmat32.obj inffas32.obj "
				OutputFile=".\zlibDllWithoutCrtDll\zlibwapi.dll"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				IgnoreAllDefaultLibraries="FALSE"
				ModuleDefinitionFile=".\zlibvc.def"
				ProgramDatabaseFile=".\zlibDllWithoutCrtDll/zlibwapi.pdb"
				GenerateMapFile="TRUE"
				MapFileName=".\zlibDllWithoutCrtDll/zlibwapi.map"
				SubSystem="2"
				OptimizeForWindows98="1"
				ImportLibrary=".\zlibDllWithoutCrtDll/zlibwapi.lib"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\zlibDllWithoutCrtDll/zlibvc.tlb"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1036"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
		</Configuration>
		<Configuration
			Name="ReleaseAxp|Win32"
			OutputDirectory=".\zlibvc__"
			IntermediateDirectory=".\zlibvc__"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="WIN32,ZLIB_WINAPI"
				StringPooling="TRUE"
				ExceptionHandling="FALSE"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="TRUE"
				PrecompiledHeaderFile=".\zlibvc__/zlibvc.pch"
				AssemblerOutput="2"
				AssemblerListingLocation=".\zlibvc__/"
				ObjectFile=".\zlibvc__/"
				ProgramDataBaseFileName=".\zlibvc__/"
				BrowseInformation="1"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="crtdll.lib"
				OutputFile="zlibvc__\zlibwapi.dll"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				IgnoreAllDefaultLibraries="TRUE"
				ModuleDefinitionFile=".\zlibvc.def"
				ProgramDatabaseFile=".\zlibvc__/zlibwapi.pdb"
				GenerateMapFile="TRUE"
				MapFileName=".\zlibvc__/zlibwapi.map"
				SubSystem="2"
				ImportLibrary=".\zlibvc__/zlibwapi.lib"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\zlibvc__/zlibvc.tlb"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1036"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory=".\ReleaseDll"
			IntermediateDirectory=".\ReleaseDll"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="WIN32,ZLIB_WINAPI,ASMV,ASMINF"
				StringPooling="TRUE"
				ExceptionHandling="FALSE"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="TRUE"
				PrecompiledHeaderFile=".\ReleaseDll/zlibvc.pch"
				AssemblerOutput="2"
				AssemblerListingLocation=".\ReleaseDll/"
				ObjectFile=".\ReleaseDll/"
				ProgramDataBaseFileName=".\ReleaseDll/"
				BrowseInformation="1"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions="/MACHINE:I386"
				AdditionalDependencies="gvmat32.obj inffas32.obj crtdll.lib"
				OutputFile=".\ReleaseDll\zlibwapi.dll"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				IgnoreAllDefaultLibraries="TRUE"
				ModuleDefinitionFile=".\zlibvc.def"
				ProgramDatabaseFile=".\ReleaseDll/zlibwapi.pdb"
				GenerateMapFile="TRUE"
				MapFileName=".\ReleaseDll/zlibwapi.map"
				SubSystem="2"
				OptimizeForWindows98="1"
				ImportLibrary=".\ReleaseDll/zlibwapi.lib"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\Release/zlibvc.tlb"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1036"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
		</Configuration>
	</Configurations>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;hpj;bat;for;f90">
			<File
				RelativePath=".\adler32.c">
			</File>
			<File
				RelativePath=".\compress.c">
			</File>
			<File
				RelativePath=".\crc32.c">
			</File>
			<File
				RelativePath=".\deflate.c">
			</File>
			<File
				RelativePath=".\gvmat32c.c">
				<FileConfiguration
					Name="ReleaseWithoutAsm|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCLCompilerTool"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\gzio.c">
			</File>
			<File
				RelativePath=".\infback.c">
			</File>
			<File
				RelativePath=".\inffast.c">
			</File>
			<File
				RelativePath=".\inflate.c">
			</File>
			<File
				RelativePath=".\inftrees.c">
			</File>
			<File
				RelativePath=".\ioapi.c">
			</File>
			<File
				RelativePath=".\iowin32.c">
			</File>
			<File
				RelativePath=".\trees.c">
			</File>
			<File
				RelativePath=".\uncompr.c">
			</File>
			<File
				RelativePath=".\unzip.c">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions="ZLIB_INTERNAL"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\zip.c">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions="ZLIB_INTERNAL"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\zlib.rc">
			</File>
			<File
				RelativePath=".\zlibvc.def">
			</File>
			<File
				RelativePath=".\zutil.c">
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;fi;fd">
			<File
				RelativePath=".\deflate.h">
			</File>
			<File
				RelativePath=".\infblock.h">
			</File>
			<File
				RelativePath=".\infcodes.h">
			</File>
			<File
				RelativePath=".\inffast.h">
			</File>
			<File
				RelativePath=".\inftrees.h">
			</File>
			<File
				RelativePath=".\infutil.h">
			</File>
			<File
				RelativePath=".\zconf.h">
			</File>
			<File
				RelativePath=".\zlib.h">
			</File>
			<File
				RelativePath=".\zutil.h">
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;cnt;rtf;gif;jpg;jpeg;jpe">
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
