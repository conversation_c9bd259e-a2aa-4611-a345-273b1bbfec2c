<?xml version="1.0" encoding = "Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.00"
	Name="zlibstat"
	SccProjectName=""
	SccLocalPath="">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory=".\zlibstatDebug"
			IntermediateDirectory=".\zlibstatDebug"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="WIN32;ZLIB_WINAPI"
				ExceptionHandling="FALSE"
				RuntimeLibrary="5"
				PrecompiledHeaderFile=".\zlibstatDebug/zlibstat.pch"
				AssemblerListingLocation=".\zlibstatDebug/"
				ObjectFile=".\zlibstatDebug/"
				ProgramDataBaseFileName=".\zlibstatDebug/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"
				DebugInformationFormat="1"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalOptions="/NODEFAULTLIB "
				OutputFile=".\zlibstatDebug\zlibstat.lib"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="ReleaseAxp|Win32"
			OutputDirectory=".\zlibsta0"
			IntermediateDirectory=".\zlibsta0"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="WIN32;ZLIB_WINAPI"
				StringPooling="TRUE"
				ExceptionHandling="FALSE"
				RuntimeLibrary="4"
				EnableFunctionLevelLinking="TRUE"
				PrecompiledHeaderFile=".\zlibsta0/zlibstat.pch"
				AssemblerListingLocation=".\zlibsta0/"
				ObjectFile=".\zlibsta0/"
				ProgramDataBaseFileName=".\zlibsta0/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalOptions="/NODEFAULTLIB "
				OutputFile=".\zlibsta0\zlibstat.lib"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory=".\zlibstat"
			IntermediateDirectory=".\zlibstat"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="WIN32;ZLIB_WINAPI;ASMV;ASMINF"
				StringPooling="TRUE"
				ExceptionHandling="FALSE"
				RuntimeLibrary="4"
				EnableFunctionLevelLinking="TRUE"
				PrecompiledHeaderFile=".\zlibstat/zlibstat.pch"
				AssemblerListingLocation=".\zlibstat/"
				ObjectFile=".\zlibstat/"
				ProgramDataBaseFileName=".\zlibstat/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalOptions="gvmat32.obj inffas32.obj /NODEFAULTLIB "
				OutputFile=".\zlibstat\zlibstat.lib"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="ReleaseWithoutAsm|Win32"
			OutputDirectory="zlibstatWithoutAsm"
			IntermediateDirectory="zlibstatWithoutAsm"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="WIN32;ZLIB_WINAPI"
				StringPooling="TRUE"
				ExceptionHandling="FALSE"
				RuntimeLibrary="4"
				EnableFunctionLevelLinking="TRUE"
				PrecompiledHeaderFile=".\zlibstat/zlibstat.pch"
				AssemblerListingLocation=".\zlibstatWithoutAsm/"
				ObjectFile=".\zlibstatWithoutAsm/"
				ProgramDataBaseFileName=".\zlibstatWithoutAsm/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				AdditionalOptions=" /NODEFAULTLIB "
				OutputFile=".\zlibstatWithoutAsm\zlibstat.lib"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
		</Configuration>
	</Configurations>
	<Files>
		<Filter
			Name="Source Files"
			Filter="">
			<File
				RelativePath=".\adler32.c">
			</File>
			<File
				RelativePath=".\compress.c">
			</File>
			<File
				RelativePath=".\crc32.c">
			</File>
			<File
				RelativePath=".\deflate.c">
			</File>
			<File
				RelativePath=".\gvmat32c.c">
			</File>
			<File
				RelativePath=".\gzio.c">
			</File>
			<File
				RelativePath=".\infback.c">
			</File>
			<File
				RelativePath=".\inffast.c">
			</File>
			<File
				RelativePath=".\inflate.c">
			</File>
			<File
				RelativePath=".\inftrees.c">
			</File>
			<File
				RelativePath=".\ioapi.c">
			</File>
			<File
				RelativePath=".\trees.c">
			</File>
			<File
				RelativePath=".\uncompr.c">
			</File>
			<File
				RelativePath=".\unzip.c">
			</File>
			<File
				RelativePath=".\zip.c">
			</File>
			<File
				RelativePath=".\zlib.rc">
			</File>
			<File
				RelativePath=".\zlibvc.def">
			</File>
			<File
				RelativePath=".\zutil.c">
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
