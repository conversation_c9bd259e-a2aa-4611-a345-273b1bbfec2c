﻿#include "stdafx.h"
#include <algorithm>
#include "../iShareDll/crc32.hpp"
#include "../iShareDll/stringsystem.h"
#include "../iShareDll/Blowfish.h"
#include "../iShareDll/LogSystem.h"
#include "Register.h"
#ifdef _MSC_VER
#if defined(OEM_USBKEY)
#ifdef _WIN64
#pragma comment(lib, "RocketArm/x64/Dongle_s.lib")
#else
#pragma comment(lib, "RocketArm/Dongle_s.lib")
#endif
#elif defined(OEM_USBKEY_NET)
#ifdef _WIN64
#pragma comment(lib, "RocketArm/net/x64/NetRockeyARM.lib")
#else
#pragma comment(lib, "RocketArm/net/NetRockeyARM.lib")
#endif
//飞天诚信 rockey4s
#elif defined(OEM_USBKEY4S)
#ifdef _WIN64
#pragma comment(lib, "RocketArm/x64/Ry4S_x64.lib")
#else
#pragma comment(lib, "RocketArm/Ry4S.lib")
#endif
#endif
#endif

CRegister::CRegister(boost::asio::io_service& io_service, CDatabaseSQLBase& dbGameSql)
	: m_dbGameSql(dbGameSql)
	, m_RealTime(0)
	, m_OldTicket(0)
	, m_nTimeZone(0)
	, m_MutexDump()
	, m_tTimerUSB(io_service, m_MutexDump)
	, m_USB_HID("")
	, m_dwCheckTimes(0)
	, m_hDongle(0)
{
}

CRegister::~CRegister()
{
}

bool CRegister::Start()
{
	auto sHardwardID = GetUsbHardwareID();
	//第一次检查，未打开加密狗不复位注册信息
	if (sHardwardID.size() > 0 && sHardwardID.compare(m_dbGameSql.GetDatabase().GetOptionWstr(PRINTER_OPTION_HARDWARECODE)) != 0) {
		m_dbGameSql.SetOption(PRINTER_OPTION_HARDWARECODE, sHardwardID);//硬件码
		ResetHardware(0, _T(""));
	}
	m_nTimeZone = GetTimeZone() * 60 * 60; //utc到本地时间的误差,秒
	GetRealTime();//核对真实时间
	CheckRegistration();
	m_tTimerUSB.Start(GetDurationFromSeconds(IsUsbOpen() ? USB_KEY_CHECKSECONDS_GOOD : USB_KEY_CHECKSECONDS_LOST),
		boost::bind(&CRegister::OnTimeCheckUSB, this));
	return true;
}

bool CRegister::Close()
{
	m_tTimerUSB.Close();
	CloseDongle();
	return true;
}

bool CRegister::CheckRegisterLogin(DWORD m_dwOnlineCount)
{
	unsigned short allownum(3);//限制台数
	if (m_reginfo.tRegExpiredate > 0) {//先检查时间
		if (GetRealTime() + m_nTimeZone <= m_reginfo.tRegExpiredate
			|| UNLIMIT_TIME_T_2030_1_1 == m_reginfo.tRegExpiredate) {//未过期, 将UFC过期日期认为是本地过期日期对待
			if (m_reginfo.bRegistered > ISHAREDISK_NOREGISTER && m_reginfo.bRegistered <= ISHAREDISK_ENTERPRISE) {
				allownum = m_reginfo.nRegNum;//认注册台数
			}
			else {
				allownum = REGISTER_TESTCOUNT;//测试期间内,认测试台数
				//WRITE_ISCSILOG(_T("CheckRegisterLogin error, bRegistered: ") << m_reginfo.bRegistered << _T(", allownum: ") << allownum);
			}
		}
		else {
			WRITE_ISCSILOG(_T("CheckRegisterLogin error, Expiredate: ") << m_reginfo.tRegExpiredate << _T(", Real time: ") << (GetRealTime() + m_nTimeZone));
		}
	}
	//允许数量相等,客户机全开的情况下,最后登录的客户机有困难
	bool ret = (m_dwOnlineCount <= allownum);
#ifdef _DEBUG
	ret = true;
#endif
	if (!ret) {
		WRITE_ISCSILOG(_T("CheckRegisterLogin error, OnlineCount: ") << m_dwOnlineCount << _T(", allownum: ") << allownum);
	}
	return ret;
}

bool CRegister::OnRegisterCodeChange()
{
	SaveRegisterCodeToUsb();
	CheckRegistration();//开始注册检查
	return true;
}

bool CRegister::CheckRegistration()
{
	ReadUsbLicenseCode();
	unsigned char RegCode[sizeof(ISHAREDISK_REGISTRATION)];//注册码
	unsigned char KeyArray[sizeof(ISHAREDISK_REGISTRATION)];//key
	RtlZeroMemory(RegCode, sizeof(RegCode));
	RtlZeroMemory(KeyArray, sizeof(KeyArray));
	std::wstring regitercode = m_dbGameSql.GetDatabase().GetOptionWstr(PRINTER_OPTION_REGISTRATIONCODE);//获取注册码
	if (regitercode.size() == sizeof(RegCode) * 2) {//用户输入了注册码,开始检查
		hexwstring2byte(regitercode, RegCode, sizeof(RegCode));
	}

	std::wstring hardcode = m_dbGameSql.GetDatabase().GetOptionWstr(PRINTER_OPTION_HARDWARECODE);//硬件码
	DWORD64 tRegExpiredate = m_dbGameSql.GetDatabase().GetOptionVal64(PRINTER_OPTION_REGISTEDDATA);//试用日期
	m_reginfo.reset();//注册信息复位
	if (memcmp(RegCode, KeyArray, sizeof(ISHAREDISK_REGISTRATION)) != 0 //注册码有数据
		&& GetGamebtKeyArray(hardcode, KeyArray, sizeof(KeyArray)))
	{//使用硬件代码作为key,解密注册码为明文
		ISHAREDISK_REGISTRATION tmp_reginfo;		//注册信息
		CBlowFish blowfish(KeyArray, sizeof(KeyArray));
		if (blowfish.Decrypt(RegCode, (unsigned char*)&tmp_reginfo, sizeof(tmp_reginfo))
			&& tmp_reginfo.CheckFormat())//格式正确
			m_reginfo = tmp_reginfo;
	}
	if (m_reginfo.tRegExpiredate == 0) {//检查过期日期
		if (tRegExpiredate == 0) {//计算一个过期日期
			auto nowtime = GetRealTime();
			nowtime += REGISTER_TESTTIME + m_nTimeZone;//矫正测试日期为本地日期
			m_reginfo.tRegExpiredate = nowtime;
		}
		else {
			m_reginfo.tRegExpiredate = tRegExpiredate;//使用以前保留的试用日期,避免无限试用
		}
	}
	SaveRegisterInfo();//保存注册信息
	return false;//只检查一次
}

void CRegister::SaveRegisterInfo()
{
	WRITE_ISCSILOG(_T("SaveRegisterInfo bRegistered: ") << m_reginfo.bRegistered << _T(", bNoNetCheck") << m_reginfo.bNoNetCheck
		<< _T(", nRegNum: ") << m_reginfo.nRegNum << _T(", tRegExpiredate: ") << m_reginfo.tRegExpiredate
		<< _T(", nSupportVersion") << m_reginfo.nSupportVersion << _T(", ServerSystemVersion: ") << m_reginfo.GetServerSystemVersion()
		<< _T(", ClientSystemVersion") << m_reginfo.GetClientSystemVersion());
	m_dbGameSql.SetOption(PRINTER_OPTION_REGISTERED, m_reginfo.bRegistered);
	// 	m_pServer.m_pOption.SetOptionVal(ISHAREDISK_OPTION_NONETCHECK, m_reginfo.bNoNetCheck);
	m_dbGameSql.SetOption(PRINTER_OPTION_REGISTEDNUM, m_reginfo.nRegNum);
	m_dbGameSql.SetOption(PRINTER_OPTION_REGISTEDDATA, (int64_t)m_reginfo.tRegExpiredate);
	// 	m_pServer.m_pOption.SetOptionVal(ISHAREDISK_OPTION_SUPPORTVERSION, m_reginfo.nSupportVersion);
	// 	m_pServer.m_pOption.SetOptionVal(ISHAREDISK_OPTION_SERVERSYSTEMVER, m_reginfo.GetServerSystemVersion());
	// 	m_pServer.m_pOption.SetOptionVal(ISHAREDISK_OPTION_CLIENTSYSTEMVER, m_reginfo.GetClientSystemVersion());
}

std::wstring CreateHardwareID(DONGLE_INFO* pDongleInfo)
{
	//boost::uuids::detail::sha1 sha;        // 声明摘要对象
	//sha.process_bytes(pDongleInfo->m_HID, sizeof(pDongleInfo->m_HID));
	//unsigned int digest[5];                 // unsigned int(4个字节) * 5个
	//sha.get_digest(digest);
	SHA1_DIGEST_TYPE digest;
	calcu_sha1(pDongleInfo->m_HID, sizeof(pDongleInfo->m_HID), digest);
	std::wstring hardwareid;
	byte2hexwstring((unsigned char*)digest, sizeof(digest), hardwareid);
	return hardwareid;
}

std::wstring CRegister::GetUsbHardwareID()
{
	std::wstring hardwareid(_T(""));
	int nCount = 0;
	auto dwRet = Dongle_Enum(NULL, &nCount);//枚举锁的数量
#if defined(_DEBUG)
	WRITE_ISCSILOG(_T("Dongle_Enum ret: ") << dwRet << _T(", nCount: ") << nCount);
#endif
	if (dwRet == DONGLE_SUCCESS) {
		std::vector<char> buffer(nCount * sizeof(DONGLE_INFO), 0);
		DONGLE_INFO* pDongleInfo = (DONGLE_INFO*)buffer.data();
		dwRet = Dongle_Enum(pDongleInfo, &nCount);//获取加密锁的相关信息
#if defined(_DEBUG)
		WRITE_ISCSILOG(_T("Dongle_Enum ret: ") << dwRet << _T(", nCount: ") << nCount);
#endif
		if (dwRet == DONGLE_SUCCESS) {
			int i = 0;
			for (; i < nCount; i++) {
				//0xFF标准版, 0x00时钟锁, 0x01带时钟的U盘锁, 0x02标准U盘锁
				if (
#if !defined(_MSC_VER) && !defined(OEM_USBKEY_NET)
					(pDongleInfo[i].m_Type == 0xFF || pDongleInfo[i].m_Type == 0x00) &&
#endif
					pDongleInfo[i].m_PID == 1276444020) //产品ID：4C14FD74
					break;
			}
			if (i < nCount) {//找到符合条件的锁,开始验证锁
				dwRet = Dongle_Open(&m_hDongle, MAKEDWORD(i, 0));//打开第1把锁
#if defined(_DEBUG)
				WRITE_ISCSILOG(_T("Dongle_Open ret: ") << dwRet);
#endif
				if (dwRet == DONGLE_SUCCESS) {
					int nMainRet = 0;//返回值
					unsigned char HardwareCode[SHA1_CODE_SIZE];//硬件码
					RtlZeroMemory(HardwareCode, SHA1_CODE_SIZE);
					dwRet = Dongle_RunExeFile(m_hDongle, 0x0001, HardwareCode, SHA1_CODE_SIZE, &nMainRet);
					bool is_all_zero = std::all_of(std::begin(HardwareCode), std::end(HardwareCode), [](unsigned char x)->bool { return x == 0; });
#if defined(_DEBUG)
					WRITE_ISCSILOG(_T("Dongle_RunExeFile ret: ") << dwRet << _T(", nMainRet: ") << nMainRet);
#endif
					if (dwRet == DONGLE_SUCCESS && (nMainRet == SHA1_CODE_SIZE || !is_all_zero)) {//从加密key里得到硬件码
						byte2hexwstring(HardwareCode, sizeof(HardwareCode), hardwareid);
						if (hardwareid.compare(CreateHardwareID(pDongleInfo + i)) == 0) {//认证的USB key 确认
							Dongle_LEDControl(m_hDongle, LED_BLINK);//确认后开始闪烁
							byte2hexstring(pDongleInfo[i].m_HID, sizeof(pDongleInfo[i].m_HID), m_USB_HID);
							WRITE_ISCSILOG(_T("Found USB key ID: ") << m_USB_HID.c_str());
							unsigned char KeyArray[sizeof(ISHAREDISK_REGISTRATION)];//key
							memset(KeyArray, 8, sizeof(KeyArray));
							CBlowFish blowfish(KeyArray, sizeof(KeyArray));
							memcpy(KeyArray, pDongleInfo[i].m_HID, sizeof(pDongleInfo[i].m_HID));
							memcpy(KeyArray + sizeof(pDongleInfo[i].m_HID), pDongleInfo[i].m_BirthDay, sizeof(pDongleInfo[i].m_BirthDay));
							RtlZeroMemory(HardwareCode, SHA1_CODE_SIZE);
							blowfish.Encrypt(KeyArray, HardwareCode, sizeof(ISHAREDISK_REGISTRATION));
							byte2hexwstring(HardwareCode, sizeof(ISHAREDISK_REGISTRATION), hardwareid);//得到加密的硬件码
							WRITE_ISCSILOG(_T("Found USB encrypt key ID: ") << hardwareid);
						}
						else {
							hardwareid = _T("");//复位,非认证的USB key
						}
					}
					if (hardwareid.size() == 0)//不是认证的usb,读取硬件号失败
						CloseDongle();
				}
			}
		}
	}
	return hardwareid;
}

void CRegister::CloseDongle()
{
	if (IsUsbOpen()) {
		Dongle_LEDControl(m_hDongle, LED_OFF);//确认后开始闪烁
		Dongle_Close(m_hDongle);//关闭加密锁
		m_hDongle = NULL;
		m_USB_HID.clear();
	}
}

bool CRegister::IsUsbOpen()
{
	return (m_USB_HID.size() > 0);
}

bool CRegister::ReadUsbLicenseCode()
{
	if (IsUsbOpen()) {
		unsigned char RegCode[sizeof(ISHAREDISK_REGISTRATION)];//注册码
		memset(RegCode, 0xFF, sizeof(RegCode));  //Key里的初始化数据都为 0xFF
		auto dwRet = Dongle_ReadData(m_hDongle, sizeof(RegCode), RegCode, sizeof(RegCode));//注册码保存到USB key
#if defined(_DEBUG)
		WRITE_ISCSILOG(_T("Dongle_ReadData ret: ") << dwRet);
#endif
		if (dwRet == DONGLE_SUCCESS) {
			std::wstring regitercode(_T(""));
			int i(0);
			for (; i < sizeof(RegCode); i++) {
				if (RegCode[i] != 0xFF)
					break;
			}
			if (i < sizeof(RegCode)) {//注册码有数据
				byte2hexwstring(RegCode, sizeof(RegCode), regitercode);
				WRITE_ISCSILOG(_T("Found USB encrypt license key: ") << regitercode);
			}
			if (regitercode.compare(m_dbGameSql.GetDatabase().GetOptionWstr(PRINTER_OPTION_REGISTRATIONCODE)) != 0) {
				m_dbGameSql.SetOption(PRINTER_OPTION_REGISTRATIONCODE, regitercode);		//注册码
			}
		}
		return true;
	}
	return false;
}

void CRegister::ReadUsbRealTime()
{
	if (IsUsbOpen()) {
#ifdef _MSC_VER
		DWORD realtime(0);
#else
		unsigned long realtime(0);
#endif
		if (Dongle_GetUTCTime(m_hDongle, &realtime) == DONGLE_SUCCESS && realtime)
			m_RealTime = realtime;
	}
}

bool CRegister::SaveRegisterCodeToUsb()
{
	if (IsUsbOpen()) {
		unsigned char RegCode[sizeof(ISHAREDISK_REGISTRATION)];//注册码
		memset(RegCode, 0xFF, sizeof(RegCode));//复位
		std::wstring regitercode = m_dbGameSql.GetDatabase().GetOptionWstr(PRINTER_OPTION_REGISTRATIONCODE);
		if (regitercode.size() == sizeof(RegCode) * 2) {
			hexwstring2byte(regitercode, RegCode, sizeof(RegCode));
		}
		WRITE_ISCSILOG(_T("Write Register license code to USB key: ") << regitercode);
		return (DONGLE_SUCCESS == Dongle_WriteData(m_hDongle, sizeof(RegCode), RegCode, sizeof(RegCode)));//注册码保存到USB key
	}
	return false;
}

std::time_t CRegister::GetRealTime()
{
	auto newtick = GetTickCount();
	if (m_RealTime == 0 || m_OldTicket == 0) {
		ReadUsbRealTime();
		if (m_RealTime == 0) {
			std::time(&m_RealTime);//直接返回当前假日期
		}
	}
	else if (newtick >= m_OldTicket) {//加上流失的时间
		m_RealTime += (newtick - m_OldTicket) / 1000;
	}
	else if (newtick < m_OldTicket) {//超过49天复位了
		m_RealTime += ((DWORD)(-1) - m_OldTicket + newtick) / 1000;
	}
	m_OldTicket = newtick;
	return m_RealTime;
}

int CRegister::GetTimeZone()
{
	int timezone(0);
	std::time_t nowtime(0);
	struct tm localtm1 = *localtime(&nowtime);
	if (localtm1.tm_year == 70 && localtm1.tm_mon == 0 && localtm1.tm_mday == 1) //1970-1-1
		timezone = localtm1.tm_hour;       //正时区
	else
		timezone = localtm1.tm_hour - 24;  //1969-12-31 负时区
	WRITE_ISCSILOG(_T("Time Zone: ") << timezone);
	return timezone;
}

bool CRegister::GetGamebtKeyArray(const std::wstring& hardcode, unsigned char* keyarray, int keylen)
{
	if (hardcode.size() == sizeof(ISHAREDISK_REGISTRATION) * 2
		&& keyarray != NULL && keylen == sizeof(ISHAREDISK_REGISTRATION)
		&& IsUsbOpen())
	{
		unsigned char tmpKeyArray[sizeof(ISHAREDISK_REGISTRATION)] = { 0 };
		if (hexwstring2byte(hardcode, tmpKeyArray, sizeof(tmpKeyArray)))//先转换为byte
		{
			int nMainRet = 0;//返回值
			auto dwRet = Dongle_RunExeFile(m_hDongle, 0x0003, tmpKeyArray, sizeof(ISHAREDISK_REGISTRATION), &nMainRet);
#if defined(_DEBUG)
			WRITE_ISCSILOG(_T("Dongle_RunExeFile ret: ") << dwRet);
#endif
			if (dwRet == DONGLE_SUCCESS && nMainRet == sizeof(ISHAREDISK_REGISTRATION)) {//从加密key里得到解密key
				RtlCopyMemory(keyarray, tmpKeyArray, sizeof(ISHAREDISK_REGISTRATION));
				return true;
			}
		}
	}
	return false;
}

bool CRegister::OnTimeCheckUSB()
{
	std::wstring hardwareid(_T(""));
	bool bOldUsbGood = IsUsbOpen();
	bool bNewGood(false);
	if (bOldUsbGood) {
		m_dwCheckTimes = 0;
		bNewGood = CheckUsbGood();
		if (!bNewGood) {//加密锁已经被移除
			WRITE_ISCSILOG(_T("USB encrypt key lost!"));
			CloseDongle();//关闭加密锁
		}
	}
	else {
		hardwareid = GetUsbHardwareID();
		bNewGood = (hardwareid.size() > 0);
		//打开成功
		if (bNewGood) {
			WRITE_ISCSILOG(_T("Found USB encrypt key again"));
		}
		else {
			m_dwCheckTimes++;
		}
	}
	if ((!bOldUsbGood && bNewGood) || m_dwCheckTimes > USB_KEY_CHECKTIMES_RESET) {
		WRITE_ISCSILOG(_T("USB encrypt key save, times:") << m_dwCheckTimes << _T(", bOldUsbGood:") << bOldUsbGood << _T(", bNewGood") << bNewGood);
		m_dwCheckTimes = 0;
		// 		m_pServer.m_pOption.SetOption(ISHAREDISK_OPTION_HARDWARECODE, hardwareid);//更新硬件码
				//因为有可能保留了旧的硬件码，所以设置也不会变化。//因为有可能保留了旧的硬件码，所以设置也不会变化。
		// 		if (hardwareid.compare(m_dbGameSql.GetDatabase().GetOptionWstr(PRINTER_OPTION_HARDWARECODE)) != 0) {//硬件码有变化,复位过期日期等
		m_dbGameSql.SetOption(PRINTER_OPTION_HARDWARECODE, hardwareid);//硬件码
		ResetHardware(0, _T(""));
		CheckRegistration();
		// 			m_pServer.OnOptionChanged();
		// 		}
	}
	else if (!bNewGood) {
		//提示用户，usb出现问题
		WRITE_ISCSILOG(_T("USB encrypt key no found, warning user times:") << m_dwCheckTimes);
		// 		m_pServer.ShowError(ERRORSTATUS_USBKEYLOST, m_dwCheckTimes);
	}
	if (bOldUsbGood != bNewGood) {
		WRITE_ISCSILOG(_T("OnTimeCheckUSB start again times:") << m_dwCheckTimes << _T(", bOldUsbGood:") << bOldUsbGood << _T(", bNewGood") << bNewGood);
		m_tTimerUSB.Start(GetDurationFromSeconds(IsUsbOpen() ? USB_KEY_CHECKSECONDS_GOOD : USB_KEY_CHECKSECONDS_LOST),
			boost::bind(&CRegister::OnTimeCheckUSB, this));
		return false;//避免重复开始timer
	}
	return true;
}

bool CRegister::CheckUsbGood()
{
	return (DONGLE_SUCCESS == Dongle_LEDControl(m_hDongle, LED_BLINK));
}

void CRegister::ResetHardware(int64_t registeddata, const std::wstring& licensecode)
{
	WRITE_ISCSILOG(_T("Reset Hardware and all register information."));
	if (m_dbGameSql.GetDatabase().GetOptionVal(PRINTER_OPTION_REGISTERED) != ISHAREDISK_NOREGISTER)
		m_dbGameSql.SetOption(PRINTER_OPTION_REGISTEDDATA, (int64_t)0);		//注册过期日期
	else if (registeddata > 0)												//试用版本校验日期
		m_dbGameSql.SetOption(PRINTER_OPTION_REGISTEDDATA, registeddata);	//注册过期日期
	m_dbGameSql.SetOption(PRINTER_OPTION_REGISTRATIONCODE, licensecode);	//注册码
	m_dbGameSql.SetOption(PRINTER_OPTION_REGISTERED, 0);					//是否注册,注册无广告,免费有广告
	m_dbGameSql.SetOption(PRINTER_OPTION_REGISTEDNUM, 0);					//注册台数
}