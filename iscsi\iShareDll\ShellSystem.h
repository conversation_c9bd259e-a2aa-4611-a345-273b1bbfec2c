﻿#pragma once
#include <string>
#include <ShlObj.h>
#include <functional>
#include "basevardef.h"

BOOL WaitForEndShellExecute(LPCTSTR lpFile, LPCTSTR lpParameter = NULL, DWORD dwMilliseconds = 0, int nShow = SW_SHOWNORMAL, LPCTSTR lpDirectory = NULL, PDWORD pExitCode = NULL);
std::wstring GetSpecialFolder(int SpecialFolder);
inline std::wstring GetDesktopFolder() { return GetSpecialFolder(CSIDL_DESKTOPDIRECTORY); };
BOOL SelectFileDlg(std::wstring& lpstrFile, HWND hwndOwner = NULL, LPCTSTR lpstrFilter = _T("All(*.*)\0*.*\0\0"), LPCTSTR lpstrInitialDir = NULL, BOOL bExist = TRUE);
BO<PERSON> BrowseForFolder(std::wstring& strFolder, HWND pWndParent = NULL, LPCTSTR lplszInitialFolder = NULL, LPCTSTR lpszTitle = NULL, UINT ulFlags = BIF_RETURNONLYFSDIRS, LPINT piFolderImage = NULL);
BOOL CreateShortcut(LPCTSTR pszExePath, LPCTSTR pszWorkingDir, LPCTSTR pszDescription, LPCTSTR pszIconPath, int nIconIndex, LPCTSTR pszDestinationPath);
bool RunCmdWithReturn(const std::string& sCmd, std::function<bool(const char*)> funCallback);
BOOL RunElevated(HWND hwnd, LPCTSTR pszPath, LPCTSTR pszParameters = NULL, LPCTSTR pszDirectory = NULL);
BOOL IsProcessRunAsAdmin();

struct EnvVar {
	int csidl;
	std::wstring var_name;
};

#define USERPROFILE_VAR L"%USERPROFILE%"

//https://learn.microsoft.com/zh-cn/windows/win32/shell/csidl
//https://learn.microsoft.com/zh-cn/windows/deployment/usmt/usmt-recognized-environment-variables
const EnvVar env_test_vars[] = {
	// C:\Users\<USER>\Windows
	{CSIDL_WINDOWS, MAKELINK_INI_ENV_SYSTEMROOT},
	{0, MAKELINK_INI_ENV_CURRENT_SID},
};
const  EnvVar env_vars[] = {
	// C:\Users\<USER>\AppData\Roaming
	{CSIDL_APPDATA, L"%APPDATA%"},
	//C:\Users\<USER>\AppData\Local
	{CSIDL_LOCAL_APPDATA, L"%LOCALAPPDATA%"},
	//C:\ProgramData
	{CSIDL_COMMON_APPDATA, L"%ALLUSERSPROFILE%"},
	//C:\Users\<USER>\Users\jackie
	{CSIDL_PROFILE, USERPROFILE_VAR},

	{CSIDL_PROGRAM_FILES, L"%PROGRAMFILES%"},
	{CSIDL_PROGRAM_FILESX86, L"%PROGRAMFILES(X86)%"},
	{CSIDL_PROGRAM_FILES_COMMON, L"%COMMONPROGRAMFILES%"},
	{CSIDL_PROGRAM_FILES_COMMONX86, L"%COMMONPROGRAMFILES(X86)%"},

	{CSIDL_SYSTEM, L"%SYSTEM%"},
	{CSIDL_SYSTEM, L"%SYSTEM32%"},

	{CSIDL_WINDOWS, L"%WINDOWS%"},
	{CSIDL_WINDOWS, L"%WINDIR%"},
	{CSIDL_WINDOWS, MAKELINK_INI_ENV_SYSTEMROOT},

	{ CSIDL_COOKIES, L"%COOKIES%" },
	{ CSIDL_HISTORY, L"%HISTORY%" },
	{ CSIDL_INTERNET_CACHE, L"%INTERNET_CACHE%" },
	{ CSIDL_MYPICTURES, L"%MYPICTURES%" },
	{ CSIDL_PERSONAL, L"%PERSONAL%" },

	{ CSIDL_RECENT, L"%RECENT%" },
	{ CSIDL_SENDTO, L"%SENDTO%" },
	{ CSIDL_STARTMENU, L"%STARTMENU%" },
	{ CSIDL_STARTUP, L"%STARTUP%" },
	{ CSIDL_TEMPLATES, L"%TEMPLATES%" },
	{ CSIDL_FAVORITES, L"%FAVORITES%" },
	{ CSIDL_FONTS, L"%FONTS%" },
	{ CSIDL_NETHOOD, L"%NETHOOD%" },
	{ CSIDL_PRINTHOOD, L"%PRINTHOOD%" },
	{ CSIDL_PROGRAMS, L"%PROGRAMS%" },
	{ CSIDL_COMMON_STARTMENU, L"%COMMON_STARTMENU%" },
	{ CSIDL_COMMON_PROGRAMS, L"%COMMON_PROGRAMS%" },
	{ CSIDL_COMMON_STARTUP, L"%COMMON_STARTUP%" },
	{ CSIDL_COMMON_FAVORITES, L"%COMMON_FAVORITES%" },
	{ CSIDL_COMMON_APPDATA, L"%COMMON_APPDATA%" },
	{ CSIDL_COMMON_DOCUMENTS, L"%COMMON_DOCUMENTS%" },
	{ CSIDL_COMMON_ADMINTOOLS, L"%COMMON_ADMINTOOLS%" },
	{ CSIDL_ADMINTOOLS, L"%ADMINTOOLS%" },
	{ CSIDL_COMMON_MUSIC, L"%COMMON_MUSIC%" },
	{ CSIDL_COMMON_PICTURES, L"%COMMON_PICTURES%" },
	{ CSIDL_COMMON_VIDEO, L"%COMMON_VIDEO%" },
	{ CSIDL_RESOURCES, L"%RESOURCES%" },

	{ CSIDL_DESKTOPDIRECTORY, L"%DESKTOPDIRECTORY%"},
	{ CSIDL_COMMON_DESKTOPDIRECTORY, L"%COMMON_DESKTOPDIRECTORY%" },

	{0, L"%TEMP%"},
	{0, L"%TMP%"},
	{0, L"%SYSTEMDRIVE%"},
	{0, MAKELINK_INI_ENV_CURRENT_SID},
};

bool GetEnvVarPath(int csidl, std::wstring& value);
bool GetEnvVarValue(const std::wstring& name, std::wstring& value);
std::wstring GetProfilesPath();
std::wstring GetAllUsersProfilePath();