﻿#include "stdafx.h"
#include "../iShareDriverLib/RegKey.h"
#include "FilterSet.h"

/**
 * @brief 构造函数，初始化过滤器集合。
 */
CFilterSet::CFilterSet(void)
{
	// 初始化成员变量
}

/**
 * @brief 析构函数，释放资源。
 */
CFilterSet::~CFilterSet(void)
{
	// 清理资源
}

/**
 * @brief 加载过滤器集合。
 * @param buffer 包含过滤器集合的缓冲区
 * @return BOOLEAN 是否加载成功
 */
BOOLEAN CFilterSet::LoadFilterSet(const CSimpleBuffer& buffer)
{
	UNREFERENCED_PARAMETER(buffer);
	Reset();
	CStringW strDoc;
	strDoc.AssignFromUtf8((LPSTR)buffer.GetBuffer(), static_cast<USHORT>(buffer.GetSize()));
	if (!strDoc.IsEmpty()) {
		CIniFile ini;
		if (ini.Open(strDoc)) {
			auto pEnvSection = ini.GetSection(MAKELINK_INI_ENV_SECTION);
			if (pEnvSection) {
				m_EnvPath = *pEnvSection;
			}
			auto HiveFileList = ini.GetSection(MAKELINK_INI_HIVE_SECTION);
			if (HiveFileList) {
				m_HiveFileList = *HiveFileList;
			}
			LoadCurrentValue();
			m_bEnableFilterFile = ini.GetValueBYTE(MAKELINK_INI_FILTER_SECTION, MAKELINK_INI_FILTER_APPLICATIONLAYER, FALSE);
			m_bEnableFilterSuperUser = ini.GetValueBYTE(MAKELINK_INI_FILTER_SECTION, MAKELINK_INI_FILTER_APPLICATIONLAYER_SUPER, FALSE);
			m_bDisableFilterRegister = ini.GetValueBYTE(MAKELINK_INI_FILTER_SECTION, MAKELINK_INI_FILTER_REGISTERLAYER_DISABLE, FALSE);
			m_RegPathBlackString = ini.GetValueWstring(MAKELINK_INI_FILTER_SECTION, MAKELINK_INI_FILTER_REGISTER_BLACK, _T(""));
			m_RegPathWhiteString = ini.GetValueWstring(MAKELINK_INI_FILTER_SECTION, MAKELINK_INI_FILTER_REGISTER_WHITE, _T(""));
			m_FilePathBlack = ini.GetValueWstring(MAKELINK_INI_FILTER_SECTION, MAKELINK_INI_FILTER_FILE_BLACK, _T(""));
			m_FilePathWhite = ini.GetValueWstring(MAKELINK_INI_FILTER_SECTION, MAKELINK_INI_FILTER_FILE_WHITE, _T(""));
		}
	}
	return TRUE;
}

/**
 * @brief 读取当前CurrentControlSet的值 和 当前 HKEY_CURRENT_CONFIG 的值
 */
VOID CFilterSet::LoadCurrentValue()
{
	if (-1 == m_CurrentControlSet) {
		if (!NT_SUCCESS(ReadRegValue(NULL, REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_PATH, REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_VALUE_NAME, m_CurrentControlSet))) {
			CStringW sCurrentRegPath;
			if (NT_SUCCESS(QueryRegistryMappedPath(REGISTRY_MACHINE_SYSTEM_CURRENTCONTROLSET, sCurrentRegPath))
				&& sCurrentRegPath.GetLength() > 3) {
				auto sControlSet = sCurrentRegPath.Right(sCurrentRegPath.GetLength() - 3);
				m_CurrentControlSet = sControlSet.ToUlong();
			}
			else {
				for (ULONG i = 1; i <= 10; i++) {
					auto sControlSetPath = CStringW::Format_String(REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET, i);
					if (NT_SUCCESS(IsExistRegKey(NULL, sControlSetPath))) {
						//默认最后一个
						m_CurrentControlSet = i;
					}
					else {
						break;
					}
				}
			}
		}
	}
	if (-1 == m_CurrentHardwareConfig && -1 != m_CurrentControlSet) {
		auto currentHardwarePath = CStringW::Format_String(REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE_CONFIG, m_CurrentControlSet);
		if (!NT_SUCCESS(ReadRegValue(NULL, currentHardwarePath, REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE_CONFIG_VALUE_NAME, m_CurrentHardwareConfig))) {
			CStringW sCurrentRegPath;
			if (NT_SUCCESS(QueryRegistryMappedPath(REGISTRY_MACHINE_SYSTEM_CURRENTCONTROLSET_HARDWAREPROFILES_CURRENT, sCurrentRegPath))
				&& sCurrentRegPath.GetLength() > 4) {
				auto sCurrent = sCurrentRegPath.Right(sCurrentRegPath.GetLength() - 4);
				m_CurrentHardwareConfig = sCurrent.ToUlong();
			}
			else {
				for (ULONG i = 0; i < 10; i++) {
					auto sHardwarePath = CStringW::Format_String(REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE, m_CurrentControlSet, i);
					if (NT_SUCCESS(IsExistRegKey(NULL, sHardwarePath))) {
						//默认最后一个
						m_CurrentHardwareConfig = i;
					}
					else {
						break;
					}
				}
			}
		}
	}

	if (m_CurrentUserSid.IsEmpty()) {
		//获取当前SID
		auto sCurrentUserSid = m_EnvPath.Get(MAKELINK_INI_ENV_CURRENT_SID);
		if (sCurrentUserSid && !sCurrentUserSid->IsEmpty()) {
			m_CurrentUserSid = *sCurrentUserSid;
		}
		else {
			auto sUserProfile = m_EnvPath.Get(MAKELINK_INI_ENV_USERPROFILE);
			if (sUserProfile && !sUserProfile->IsEmpty()) {
				//检查 m_CurrentUserSid, 可能因为驱动启动太早而打不开注册表
				CRegKey regKey;
				if (NT_SUCCESS(regKey.Open(NULL, REGISTRY_MACHINE_HKLM_PROFILELIST, KEY_READ))) {
					regKey.EnumKey([&](const CRegKey& regkey, ULONG index, const CStringW& skey) {
						UNREFERENCED_PARAMETER(regkey);
						UNREFERENCED_PARAMETER(index);
						//排除 S-1-5-18 S-1-5-19 S-1-5-20
						if (skey != REGISTRY_MACHINE_USER_SID_SYSTEM
							&& skey != REGISTRY_MACHINE_USER_SID_LOCAL_SERVICE
							&& skey != REGISTRY_MACHINE_USER_SID_NETWORK_SERVICE)
						{
							CRegKey regSubKey;
							if (NT_SUCCESS(regSubKey.Open(regKey, skey, KEY_READ))) {
								CStringW sProfileImagePath;
								if (NT_SUCCESS(regSubKey.QueryExpandStringValue(REGISTRY_MACHINE_HKLM_PROFILELIST_PROFILE_IMAGE_PATH, sProfileImagePath))) {
									if (sProfileImagePath == (*sUserProfile)) {
										m_CurrentUserSid = skey;
										//找到，停止遍历
										return STATUS_UNSUCCESSFUL;
									}
								}
							}
						}
						//继续遍历
						return STATUS_SUCCESS;
						});
				}
			}
		}
	}

	//检查 m_CurrentUserSid, 可能因为驱动启动太早而打不开注册表
	CRegKey regKey;
	if (NT_SUCCESS(regKey.Open(NULL, REGISTRY_MACHINE_HKU, KEY_READ))) {
		BOOLEAN bFound(FALSE);
		CStringW sCurrentUserSid;
		regKey.EnumKey([&](const CRegKey& regkey, ULONG index, const CStringW& skey) {
			UNREFERENCED_PARAMETER(regkey);
			UNREFERENCED_PARAMETER(index);
			if (skey == m_CurrentUserSid) {
				//找到
				bFound = TRUE;
			}
			if (skey != REGISTRY_MACHINE_USER_DEFAULT && skey != REGISTRY_MACHINE_USER_SID_SYSTEM
				&& skey != REGISTRY_MACHINE_USER_SID_LOCAL_SERVICE && skey != REGISTRY_MACHINE_USER_SID_NETWORK_SERVICE
				&& sCurrentUserSid.GetLength() <= skey.GetLength() && !skey.EndWith(REGISTRY_MACHINE_HKCU_CLASSES_NAME)) {
				sCurrentUserSid = skey;
			}
			//继续遍历
			return STATUS_SUCCESS;
			});
		if (!bFound && !sCurrentUserSid.IsEmpty()) {
			m_CurrentUserSid = sCurrentUserSid;
		}
	}
}

/**
 * @brief 解析注册表路径。
 * @param lRegPathList 注册表路径列表
 * @return CStringList 解析后的注册表路径列表
 *
 *  /**
  * @brief 解析注册表路径的映射规则。
  *
  * HKEY_CLASSES_ROOT
  *   1. `\Registry\User\<SID>\Software\Classes`
  *   2. `\Registry\Machine\Software\Classes`
  *   先替换为当前用户 SID 的 Classes 路径，再替换为 HKCR 路径。
  *
  * HKEY_CURRENT_USER
  *   `\Registry\User\<SID>\`
  *   替换为当前用户 SID 对应的 HKCU 路径。
  *
  * HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Hardware Profiles\Current
  *   `\Registry\Machine\System\ControlSetXX\Hardware Profiles\YY`
  *   XX为当前ControlSet编号（如02），YY为当前HardwareConfig编号。
  *
  * HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet
  *   `\Registry\Machine\System\ControlSetXX`
  *   XX为当前ControlSet编号。
  *
  * HKEY_LOCAL_MACHINE
  *   `\Registry\Machine`
  *   替换为HKLM根路径。
  *
  * HKEY_USERS
  *   `\Registry\User`
  *   替换为HKU根路径。
  *
  * HKEY_CURRENT_CONFIG
  *   `\Registry\Machine\System\ControlSetXX\Hardware Profiles\YY\`
  *   XX为当前ControlSet编号，YY为当前HardwareConfig编号。
  */
CStringList CFilterSet::ParseRegistryPath(const CStringList& lRegPathList) const
{
	CStringList lResult;
	CStringList lRegPathRoot;

	//HKEY_CLASSES_ROOT 实际上是 HKEY_LOCAL_MACHINE\Software\Classes 和 HKEY_CURRENT_USER\Software\Classes 的联合视图。系统会首先读取 HKEY_CURRENT_USER\Software\Classes 中的信息，如果没有找到，再去 HKEY_LOCAL_MACHINE\Software\Classes 中查找。这一机制允许用户和系统分别对文件类型和 COM 类设置偏好。
	for (const auto& item : lRegPathList) {
		if (REGPATH_STARTWITH_EXACT(item, MAKELINK_INI_HKEY_CLASSES_ROOT)) {
			auto sRegPathRoot1 = item;
			auto sRegPathRoot2 = item;
			if (!m_CurrentUserSid.IsEmpty()) {
				auto sRegPath = CStringW::Format_String(REGISTRY_MACHINE_HKCU_CLASSES, (PCUNICODE_STRING)m_CurrentUserSid);
				sRegPathRoot1.Replace(MAKELINK_INI_HKEY_CLASSES_ROOT, sRegPath, TRUE);
				lRegPathRoot.push_back(sRegPathRoot1);
			}
			sRegPathRoot2.Replace(MAKELINK_INI_HKEY_CLASSES_ROOT, REGISTRY_MACHINE_HKCR, TRUE);
			lRegPathRoot.push_back(sRegPathRoot2);
		}
		else {
			auto sRegPathOrg = item;
			if (REGPATH_STARTWITH_EXACT(sRegPathOrg, MAKELINK_INI_HKEY_CURRENT_USER)) {
				if (!m_CurrentUserSid.IsEmpty()) {
					auto sRegPath = CStringW::Format_String(REGISTRY_MACHINE_HKCU, (PCUNICODE_STRING)m_CurrentUserSid);
					sRegPathOrg.Replace(MAKELINK_INI_HKEY_CURRENT_USER, sRegPath, TRUE);
				}
			}
			else if (REGPATH_STARTWITH_EXACT(sRegPathOrg, MAKELINK_INI_HKEY_LOCAL_MACHINE_CURRENT_HARDWARE)) {
				auto sRegPath = CStringW::Format_String(REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE, m_CurrentControlSet, m_CurrentHardwareConfig);
				sRegPathOrg.Replace(MAKELINK_INI_HKEY_LOCAL_MACHINE_CURRENT_HARDWARE, sRegPath, TRUE);
			}
			else if (REGPATH_STARTWITH_EXACT(sRegPathOrg, MAKELINK_INI_HKEY_LOCAL_MACHINE_CURRENTCONTROLSET)) {
				auto sRegPath = CStringW::Format_String(REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET, m_CurrentControlSet);
				sRegPathOrg.Replace(MAKELINK_INI_HKEY_LOCAL_MACHINE_CURRENTCONTROLSET, sRegPath, TRUE);
			}
			else if (REGPATH_STARTWITH_EXACT(sRegPathOrg, MAKELINK_INI_HKEY_LOCAL_MACHINE)) {
				sRegPathOrg.Replace(MAKELINK_INI_HKEY_LOCAL_MACHINE, REGISTRY_MACHINE_HKLM, TRUE);
			}
			else if (REGPATH_STARTWITH_EXACT(sRegPathOrg, MAKELINK_INI_HKEY_USERS)) {
				sRegPathOrg.Replace(MAKELINK_INI_HKEY_USERS, REGISTRY_MACHINE_HKU, TRUE);
			}
			else if (REGPATH_STARTWITH_EXACT(sRegPathOrg, MAKELINK_INI_HKEY_CURRENT_CONFIG)) {
				auto sRegPath = CStringW::Format_String(REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE, m_CurrentControlSet, m_CurrentHardwareConfig);
				sRegPathOrg.Replace(MAKELINK_INI_HKEY_CURRENT_CONFIG, sRegPath, TRUE);
			}
			lResult.push_back(sRegPathOrg);
		}
	}

	lResult.insert(lResult.end(), lRegPathRoot.begin(), lRegPathRoot.end());
	lResult.Sort();
	return lResult;
}

/**
 * @brief 查询注册表映射路径。
 * @param sRegCompletePath 完整的注册表路径
 * @param sMappedPath 映射路径
 * @return NTSTATUS 操作状态码
 */
NTSTATUS CFilterSet::QueryRegistryMappedPath(PCWSTR sRegCompletePath, CStringW& sMappedPath)
{
	UNREFERENCED_PARAMETER(sRegCompletePath);
	UNREFERENCED_PARAMETER(sMappedPath);
	CRegKey regKey;
	auto status = regKey.Open(NULL, sRegCompletePath, KEY_READ);
	if (NT_SUCCESS(status)) {
		status = regKey.GetKeyInfo(sMappedPath);
		if (!NT_SUCCESS(status)) {
			sMappedPath = regKey.GetKeyName();
			if (sMappedPath.IsEmpty()) {
				status = STATUS_UNSUCCESSFUL;
			}
		}
	}
	return status;
}

/**
 * @brief 获取所有的注册表路径。
 * 自动启动时，只会读取一项  \REGISTRY\MACHINE\HARDWARE ， hive 文件路径为空
 * @param lHiveFileList 注册表路径列表
 */
 //VOID CFilterSet::GetAllHiveFileList(CStringList& lHiveFileList)
 //{
 //	UNREFERENCED_PARAMETER(lHiveFileList);
 //	if (-1 != m_CurrentControlSet) {
 //		auto hivelistRegPath = CStringW::Format_String(REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_HIVELIST, m_CurrentControlSet);
 //		CRegKey regKey;
 //		if (NT_SUCCESS(regKey.Open(NULL, hivelistRegPath, KEY_READ))) {
 //			regKey.EnumValueInfo([&](const CRegKey& regkey, ULONG index, ULONG count, PKEY_VALUE_FULL_INFORMATION pValueInfo) {
 //				UNREFERENCED_PARAMETER(regkey);
 //				UNREFERENCED_PARAMETER(index);
 //				UNREFERENCED_PARAMETER(count);
 //				if (pValueInfo->Type == REG_SZ) {
 //					//读取值
 //					CStringW sValue;
 //					if (NT_SUCCESS(sValue.AppendBuffer((PCSTR)KEY_VALUE_DATA(pValueInfo), (USHORT)min(pValueInfo->DataLength, REG_MAX_VALUE_DATA_SIZE)))) {
 //						//注册表值末尾有0，需要去掉
 //						sValue.ReleaseBuffer(CSTRINGW_NOPOS);
 //						if (!sValue.IsEmpty()) {
 //							lHiveFileList.push_back(sValue);
 //						}
 //					}
 //				}
 //				return STATUS_SUCCESS;
 //				});
 //			regKey.Close();
 //		}
 //	}
 //}

CStringW CFilterSet::GetWindowsRoot()
{
	CStringW sWindowsRoot(L"C:\\Windows");
	auto pWindowsRoot = m_EnvPath.Get(MAKELINK_INI_ENV_SYSTEMROOT);
	if (pWindowsRoot) {
		sWindowsRoot = *pWindowsRoot;
	}
	else {
		if (!NT_SUCCESS(ReadRegValue(HKEY_LOCAL_MACHINE, L"SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion", L"SystemRoot", sWindowsRoot))) {
			//继续从ini配置文件里读取
			for (const auto& HiveFile : m_HiveFileList) {
				CPath path(HiveFile.Value);
				if (path.HaveRootHarddiskVolume()) {
					CPath windowsRoot;
					auto windowsFolder = path.GetRelative(&windowsRoot).GetRootParent();
					if (!windowsFolder.IsEmpty() && windowsFolder == CPath(L"\\Windows")) {
						sWindowsRoot = (windowsRoot + windowsFolder).c_str();
						break;
					}
				}
			}
		}
	}
	return sWindowsRoot;
}

/**
  * @brief 获取所有的临时文件夹路径。
  * @param lTempFolderList 临时文件夹路径列表
  */
VOID CFilterSet::GetAllTempFolderList(CStringList& lTempFolderList)
{
	if (-1 != m_CurrentControlSet) {
		auto envRegPath = CStringW::Format_String(REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT, m_CurrentControlSet);
		CRegKey regKey;
		if (NT_SUCCESS(regKey.Open(NULL, envRegPath, KEY_READ))) {
			CStringW sTempFolder;
			if (NT_SUCCESS(regKey.QueryExpandStringValue(REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT_TEMP, sTempFolder))
				|| NT_SUCCESS(regKey.QueryExpandStringValue(REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT_TMP, sTempFolder))) {
				lTempFolderList.push_back(sTempFolder);
			}
			regKey.Close();
		}
	}

	//读取环境变量
	auto sTemp = m_EnvPath.Get(MAKELINK_INI_ENV_TEMP);
	if (!sTemp) {
		sTemp = m_EnvPath.Get(MAKELINK_INI_ENV_TMP);
	}
	if (sTemp) {
		lTempFolderList.push_back(*sTemp);
	}
}

/**
 * @brief 重置过滤器集合状态。
 */

VOID CFilterSet::Reset() {
	m_bEnableFilterFile = FALSE;
	m_bEnableFilterSuperUser = FALSE;
	m_bDisableFilterRegister = FALSE;
	m_CurrentControlSet = (ULONG)(-1);
	m_CurrentHardwareConfig = (ULONG)(-1);
	m_CurrentUserSid.Clear();
	m_RegPathBlackString.Clear();
	m_RegPathWhiteString.Clear();
	m_FilePathBlack.Clear();
	m_FilePathWhite.Clear();
	m_EnvPath.Clear();
	m_HiveFileList.Clear();
}

/**
 * @brief 相等比较操作符。
 * @param other 另一个 CFilterSet 对象。
 * @return bool 如果两个过滤器集合相等返回 true，否则返回 false。
 */
bool CFilterSet::operator==(const CFilterSet& other) const
{
	if (this == &other) return true;

	// 利用专门的变化检查函数来判断是否相等
	// 如果文件设置或注册表设置有任何变化，则两个对象不相等
	return !IsChangedFileSet(other) && !IsChangedRegisterSet(other);
}

/**
 * @brief 检查文件过滤设置是否发生变化。
 * @param other 另一个 CFilterSet 对象。
 * @return bool 如果文件过滤设置发生变化返回 true，否则返回 false。
 */
bool CFilterSet::IsChangedFileSet(const CFilterSet& other) const
{
	if (this == &other) return false;

	// 文件过滤相关的控制标志
	if (m_bEnableFilterFile != other.m_bEnableFilterFile ||
		m_bEnableFilterSuperUser != other.m_bEnableFilterSuperUser) {
		return true;
	}

	// 系统环境相关（影响路径解析）
	if (m_CurrentControlSet != other.m_CurrentControlSet ||
		m_CurrentHardwareConfig != other.m_CurrentHardwareConfig ||
		m_CurrentUserSid != other.m_CurrentUserSid) {
		return true;
	}
	// 文件路径配置
	if (m_FilePathBlack != other.m_FilePathBlack ||
		m_FilePathWhite != other.m_FilePathWhite) {
		return true;
	}
	// 环境变量配置（影响路径解析）
	if (m_EnvPath != other.m_EnvPath) {
		return true;
	}

	// 注册表文件列表配置
	return m_HiveFileList != other.m_HiveFileList;
}

/**
 * @brief 检查注册表过滤设置是否发生变化。
 * @param other 另一个 CFilterSet 对象。
 * @return bool 如果注册表过滤设置发生变化返回 true，否则返回 false。
 */
bool CFilterSet::IsChangedRegisterSet(const CFilterSet& other) const
{
	if (this == &other) return false;

	// 注册表过滤控制标志
	if (m_bDisableFilterRegister != other.m_bDisableFilterRegister) {
		return true;
	}

	// 注册表路径配置
	return m_RegPathBlackString != other.m_RegPathBlackString ||
		m_RegPathWhiteString != other.m_RegPathWhiteString;
}

/**
 * @brief 不等比较操作符。
 * @param other 另一个 CFilterSet 对象。
 * @return bool 如果两个过滤器集合不相等返回 true，否则返回 false。
 */
bool CFilterSet::operator!=(const CFilterSet& other) const
{
	return !(*this == other);
}

/**
 * @brief 赋值操作符。
 * @param other 另一个 CFilterSet 对象。
 * @return CFilterSet& 当前对象的引用。
 */
CFilterSet& CFilterSet::operator=(const CFilterSet& other)
{
	if (this == &other) return *this;

	// 创建临时对象（异常安全）
	CFilterSet temp(other);

	// 交换内容（不会抛出异常）
	swap(temp);

	return *this;
}

/**
 * @brief 交换两个 CFilterSet 对象的内容。
 * @param other 另一个 CFilterSet 对象。
 */
void CFilterSet::swap(CFilterSet& other) noexcept
{
	// 交换基本类型成员
	BYTE tempByte = m_bEnableFilterFile;
	m_bEnableFilterFile = other.m_bEnableFilterFile;
	other.m_bEnableFilterFile = tempByte;

	tempByte = m_bEnableFilterSuperUser;
	m_bEnableFilterSuperUser = other.m_bEnableFilterSuperUser;
	other.m_bEnableFilterSuperUser = tempByte;

	tempByte = m_bDisableFilterRegister;
	m_bDisableFilterRegister = other.m_bDisableFilterRegister;
	other.m_bDisableFilterRegister = tempByte;

	tempByte = m_bReserved;
	m_bReserved = other.m_bReserved;
	other.m_bReserved = tempByte;

	ULONG tempUlong = m_CurrentControlSet;
	m_CurrentControlSet = other.m_CurrentControlSet;
	other.m_CurrentControlSet = tempUlong;

	tempUlong = m_CurrentHardwareConfig;
	m_CurrentHardwareConfig = other.m_CurrentHardwareConfig;
	other.m_CurrentHardwareConfig = tempUlong;
	m_CurrentUserSid.swap(other.m_CurrentUserSid);
	m_RegPathBlackString.swap(other.m_RegPathBlackString);
	m_RegPathWhiteString.swap(other.m_RegPathWhiteString);
	m_FilePathBlack.swap(other.m_FilePathBlack);
	m_FilePathWhite.swap(other.m_FilePathWhite);
	m_EnvPath.swap(other.m_EnvPath);
	m_HiveFileList.swap(other.m_HiveFileList);
}

/**
 * @brief 检查两个路径是否匹配（childPath 是 parentPath 的子路径或完全匹配）
 * @param childPath 子路径
 * @param parentPath 父路径（支持 * 通配符结尾）
 * @param parentPathLen 父路径长度（0表示自动计算）
 * @return TRUE 匹配，FALSE 不匹配
 */
BOOLEAN IsPathMatched(const CStringW& childPath, LPCWSTR parentPath, USHORT parentPathLen /*= 0*/)
{
	// 如果任一路径为空，则不匹配
	if (childPath.IsEmpty() || parentPath == NULL || *parentPath == L'\0') {
		return FALSE;
	}

	// 获取父路径长度
	USHORT parentLen = parentPathLen;
	if (parentLen == 0) {
		parentLen = static_cast<USHORT>(wcslen(parentPath));
	}

	CStringW parentPathStr(parentPath, parentLen);
	return IsPathMatched(childPath, parentPathStr);
}

/**
 * @brief 检查两个路径是否匹配（childPath 是 parentPath 的子路径或完全匹配）
 * @param childPath 子路径
 * @param parentPath 父路径（支持 * 通配符结尾）
 * @return TRUE 匹配，FALSE 不匹配
 */
BOOLEAN IsPathMatched(const CStringW& childPath, const CStringW& parentPath)
{
	// 如果任一路径为空，则不匹配
	if (childPath.IsEmpty() || parentPath.IsEmpty()) {
		return FALSE;
	}

	// 检查 parentPath 是否以 * 结尾（通配符匹配）
	BOOLEAN bWildcard = (parentPath.At(parentPath.size() - 1) == ASTERISK_WCHAR);
	USHORT compareLength = bWildcard ? (parentPath.size() - 1) : parentPath.size();

	// 如果子路径长度小于需要比较的长度，则不匹配, 不支持只有 * 通配符
	if (childPath.GetLength() < compareLength || compareLength == 0) {
		return FALSE;
	}

	// 检查前缀是否匹配
	CStringW parentPrefix = bWildcard ? parentPath.Left(compareLength) : parentPath;
	if (!childPath.StartWith(parentPrefix)) {
		return FALSE;
	}

	// 完全匹配前缀部分
	if (childPath.GetLength() == compareLength) {
		return TRUE;
	}

	// 如果是通配符匹配
	if (bWildcard) {
		// 通配符匹配：检查剩余部分不包含路径分隔符
		for (USHORT i = compareLength; i < childPath.GetLength(); i++) {
			if (childPath.At(i) == SLASH_WCHAR) {
				return FALSE; // 通配符不能跨越目录层级
			}
		}
		return TRUE;
	}

	// 非通配符匹配，子路径必须以 '\\' 分隔, 或者 父路径以  '\\' 结尾
	return childPath.size() > parentPath.size() &&
		(childPath.At(parentPath.size()) == SLASH_WCHAR || parentPath.At(parentPath.size() - 1) == SLASH_WCHAR);
}