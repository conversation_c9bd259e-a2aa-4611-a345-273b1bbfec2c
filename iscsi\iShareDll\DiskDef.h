﻿#pragma once
#include <stdint.h>
#include <string>
#include "linux_type.hpp"
#if (defined(_WIN32) || defined(_WIN64))
#include <winioctl.h>
inline PDISK_PARTITION_INFO DiskGeometryGetPartitionVer(PDISK_GEOMETRY_EX pDiskGeometryEx) {
	return ((PDISK_PARTITION_INFO)((pDiskGeometryEx)->Data));
}
#else
typedef enum _PARTITION_STYLE
{
	PARTITION_STYLE_MBR,
	PARTITION_STYLE_GPT,
	PARTITION_STYLE_RAW
} PARTITION_STYLE;
#endif

#define PAGE_SIZE 0x1000
#ifndef SECSIZE
#define SECSIZE (0x200) // 默认扇区大小
#endif
#ifndef SECSIZE_4K
#define SECSIZE_4K (0x1000) // 默认扇区大小
#endif
#define MBRSIGN (0xAA55)  // mbr标示
#define BITMAP_BYTESIZE 8 // 位图里,每个字节的大小
// fat32相关
#define FAT32_STARTCLUTER_NO 2
#define FAT32_ENDCLUTER_VALUE 0x0FFFFFFF
#define FAT32_BADCLUTER_VALUE 0x0FFFFFF7
#define FAT32_DBR_NAME "FAT32"
// NTFS相关
#define NTFS_DBR_NAME "NTFS"
// 分区类型
#define PARTITION_TYPE_EXTEND 0x05
#define PARTITION_TYPE_EXTENDWIN95 0x0F
#define PARTITION_TYPE_NTFS 0x07
#define PARTITION_TYPE_NTFSHIDDEN 0x17
#define PARTITION_TYPE_FAT32 0x0B
#define PARTITION_TYPE_FAT32WIN95 0x0C
#define PARTITION_TYPE_FAT32HIDDEN 0x1B
#define PARTITION_TYPE_FAT32WIN95HIDDEN 0x1C
#define PARTITION_TYPE_PROTECT 0xEE

#define PARTITION_TYPE_EMPTY 0x00			   // 空分区
#define PARTITION_TYPE_FAT12_CHS 0x01		   // FAT12文件系统（使用CHS寻址）
#define PARTITION_TYPE_FAT16_CHS 0x04		   // FAT16文件系统（使用CHS寻址）
#define PARTITION_TYPE_EXTENDED_CHS 0x05	   // 扩展分区（使用CHS寻址）
#define PARTITION_TYPE_FAT16B_CHS 0x06		   // FAT16文件系统（使用CHS寻址）
#define PARTITION_TYPE_NTFS_CHS 0x07		   // NTFS文件系统（使用CHS寻址）
#define PARTITION_TYPE_FAT32_CHS 0x0B		   // FAT32文件系统（使用CHS寻址）
#define PARTITION_TYPE_FAT32_LBA 0x0C		   // FAT32文件系统（使用LBA寻址）
#define PARTITION_TYPE_FAT16_LBA 0x0E		   // FAT16文件系统（使用LBA寻址）
#define PARTITION_TYPE_EXTENDED_LBA 0x0F	   // 扩展分区（使用LBA寻址）
#define PARTITION_TYPE_NTFS_LBA 0x27		   // NTFS文件系统（使用LBA寻址）
#define PARTITION_TYPE_FAT32X_LBA 0x42		   // FAT32X文件系统（使用LBA寻址）
#define PARTITION_TYPE_FAT16X_LBA 0x42		   // FAT16X文件系统（使用LBA寻址）
#define PARTITION_TYPE_LINUX_SWAP 0x82		   // Linux交换分区
#define PARTITION_TYPE_LINUX_EXT2 0x83		   // Linux EXT2/EXT3文件系统
#define PARTITION_TYPE_LINUX_LVM 0x8E		   // Linux LVM分区
#define PARTITION_TYPE_BIOS_BOOT 0xEE		   // BIOS引导分区
#define PARTITION_TYPE_GPT 0xEE				   // GPT分区类型
#define PARTITION_TYPE_MICROSOFT_RESERVED 0xEE // Microsoft保留分区

enum
{
	SYSID_UNUSED = 0,	// 未使用的系统标识，取值: 0
	SYSID_EXTENDED = 5, // 扩展分区的系统标识，取值: 5
	SYSID_BIGFAT = 6,	// 大型FAT分区的系统标识，取值: 6
	SYSID_IFS = 7		// IFS分区的系统标识，取值: 7
};

#pragma pack(1)

//
// Boot record disk partition table entry structure format.
//

#ifndef PARTITION_DESCRIPTOR_DEFINED
#define PARTITION_DESCRIPTOR_DEFINED
typedef struct _PARTITION_DESCRIPTOR
{
	UCHAR ActiveFlag;							// 0 引导标志，表示分区是否可引导
	UCHAR StartingTrack;						// 1 起始磁道号，未使用
	UCHAR StartingCylinderLsb;					// 2 起始柱面号（低字节），未使用
	UCHAR StartingCylinderMsb;					// 3 起始柱面号（高字节），未使用
	UCHAR PartitionType;						// 4 分区类型，12 位 FAT、16 位 FAT 等
	UCHAR EndingTrack;							// 5 结束磁道号，未使用
	UCHAR EndingCylinderLsb;					// 6 结束柱面号（低字节），未使用
	UCHAR EndingCylinderMsb;					// 7 结束柱面号（高字节），未使用
	UCHAR StartingSectorLsb0;					// 8 隐藏扇区数（低字节）
	UCHAR StartingSectorLsb1;					// 9 隐藏扇区数（中低字节）
	UCHAR StartingSectorMsb0;					// 10 隐藏扇区数（中高字节）
	UCHAR StartingSectorMsb1;					// 11 隐藏扇区数（高字节）
	UCHAR PartitionLengthLsb0;					// 12 分区长度（低字节），以扇区为单位
	UCHAR PartitionLengthLsb1;					// 13 分区长度（中低字节）
	UCHAR PartitionLengthMsb0;					// 14 分区长度（中高字节）
	UCHAR PartitionLengthMsb1;					// 15 分区长度（高字节）
} PARTITION_DESCRIPTOR, * PPARTITION_DESCRIPTOR; // 16 分区描述符结构体定义
#endif // PARTITION_DESCRIPTOR_DEFINED

#define PSTART(p) (                          \
	(ULONG)((p)->StartingSectorLsb0) +       \
	(ULONG)((p)->StartingSectorLsb1 << 8) +  \
	(ULONG)((p)->StartingSectorMsb0 << 16) + \
	(ULONG)((p)->StartingSectorMsb1 << 24))

#define PLENGTH(p) (                          \
	(ULONG)((p)->PartitionLengthLsb0) +       \
	(ULONG)((p)->PartitionLengthLsb1 << 8) +  \
	(ULONG)((p)->PartitionLengthMsb0 << 16) + \
	(ULONG)((p)->PartitionLengthMsb1 << 24))

//
// Number of partition table entries
//

#define NUM_PARTITION_TABLE_ENTRIES 4

//
// Partition table record and boot signature offsets in 16-bit words.
//

#define PARTITION_TABLE_OFFSET (0x1be / 2)
#define BOOT_SIGNATURE_OFFSET ((0x200 / 2) - 1)

//
// Boot record signature value.
//

#define BOOT_RECORD_SIGNATURE MBRSIGN

//
// Initial size of the Partition list structure.
//

#define PARTITION_BUFFER_SIZE 2048

//
// Partition active flag - i.e., boot indicator
//

#define PARTITION_ACTIVE_FLAG 0x80

#ifndef PACKED_BOOT_SECTOR_MBR_DEFINED
#define PACKED_BOOT_SECTOR_MBR_DEFINED
typedef struct _PACKED_BOOT_SECTOR_MBR
{
	UCHAR BootStrap[0x1B8];										  // 0 启动代码
	ULONG dwDiskMark;											  // 440 磁盘签名
	UCHAR dwReserved[2];										  // 444 保留
	PARTITION_DESCRIPTOR bsPartEnts[NUM_PARTITION_TABLE_ENTRIES]; // 446 分区描述符数组
	USHORT bsSignature;											  // 510 boot block 签名 (0xAA55h)
} PACKED_BOOT_SECTOR_MBR, * PPACKED_BOOT_SECTOR_MBR;				  // 512 打包引导扇区 MBR 结构体定义
#endif // PACKED_BOOT_SECTOR_MBR_DEFINED
//
//  The following types and macros are used to help unpack the packed and
//  misaligned fields found in the Bios parameter block
//

typedef union _UCHAR1
{
	UCHAR Uchar[1];
	UCHAR ForceAlignment;
} UCHAR1, * PUCHAR1;

typedef union _UCHAR2
{
	UCHAR Uchar[2];
	USHORT ForceAlignment;
} UCHAR2, * PUCHAR2;

typedef union _UCHAR4
{
	UCHAR Uchar[4];
	ULONG ForceAlignment;
} UCHAR4, * PUCHAR4;

//
//  This macro copies an unaligned src byte to an aligned dst byte
//

#define CopyUchar1(Dst, Src)                               \
	{                                                      \
		*((UCHAR1 *)(Dst)) = *((UNALIGNED UCHAR1 *)(Src)); \
	}

//
//  This macro copies an unaligned src word to an aligned dst word
//

#define CopyUchar2(Dst, Src)                               \
	{                                                      \
		*((UCHAR2 *)(Dst)) = *((UNALIGNED UCHAR2 *)(Src)); \
	}

//
//  This macro copies an unaligned src longword to an aligned dsr longword
//

#define CopyUchar4(Dst, Src)                               \
	{                                                      \
		*((UCHAR4 *)(Dst)) = *((UNALIGNED UCHAR4 *)(Src)); \
	}

//
//  Define the Packed and Unpacked BIOS Parameter Block
//

typedef struct _PACKED_BIOS_PARAMETER_BLOCK
{
	UCHAR BytesPerSector[2];	// offset = 0x000  0	每扇区字节数
	UCHAR SectorsPerCluster[1]; // offset = 0x002  2	每族扇区数
	UCHAR ReservedSectors[2];	// offset = 0x003  3	保留扇区数
	UCHAR Fats[1];				// offset = 0x005  5	FAT表个数，一般为2，较小分区只有1个
	UCHAR RootEntries[2];		// offset = 0x006  6	根目录最多可以容纳的目录项数，fat12/16为512，Fat32不使用，为0
	UCHAR Sectors[2];			// offset = 0x008  8	扇区总数，分区小于32MB使用此处，大于32MB，使用LargeSectors
	UCHAR Media[1];				// offset = 0x00A 10	介质描述符
	UCHAR SectorsPerFat[2];		// offset = 0x00B 11	每个FAT表的大小扇区数(FAT12/16使用，FAT32不使用为0)
	UCHAR SectorsPerTrack[2];	// offset = 0x00D 13	每磁道扇区数
	UCHAR Heads[2];				// offset = 0x00F 15	磁头数
	UCHAR HiddenSectors[4];		// offset = 0x011 17	分区前已用扇区数，也称为隐藏扇区数，指DBR扇区相对于磁盘0号扇区的扇区偏移
	UCHAR LargeSectors[4];		// offset = 0x015 21	文件系统扇区总数（大于32MB）
} PACKED_BIOS_PARAMETER_BLOCK;	// sizeof = 0x019 25

typedef PACKED_BIOS_PARAMETER_BLOCK* PPACKED_BIOS_PARAMETER_BLOCK;

typedef struct BIOS_PARAMETER_BLOCK_
{
	USHORT BytesPerSector;											// 每扇区字节数 (偏移 0x000)
	UCHAR SectorsPerCluster;										// 每簇扇区数 (偏移 0x002)
	USHORT ReservedSectors;											// 保留扇区数 (偏移 0x003)
	UCHAR Fats;														// FAT 表的数量 (偏移 0x005)
	USHORT RootEntries;												// 根目录项数 (偏移 0x006)
	USHORT Sectors;													// 分区总扇区数 (偏移 0x008)
	UCHAR Media;													// 媒体描述符 (偏移 0x00A)
	USHORT SectorsPerFat;											// 每 FAT 表的扇区数 (偏移 0x00B)
	USHORT SectorsPerTrack;											// 每磁道扇区数 (偏移 0x00D)
	USHORT Heads;													// 磁头数 (偏移 0x00F)
	ULONG HiddenSectors;											// 隐藏扇区数 (偏移 0x011)
	ULONG LargeSectors;												// 大型扇区数 (偏移 0x015)
} BIOS_PARAMETER_BLOCK_ISHREDISK, * PBIOS_PARAMETER_BLOCK_ISHREDISK; // BIOS 参数块 (BPB) 结构体定义

//
//  Define the boot sector
//

typedef struct _PACKED_BOOT_SECTOR_FAT32
{
	UCHAR Jump[3];						   // offset = 0x000   0	汇编指令，跳转到引导代码处
	UCHAR Oem[8];						   // offset = 0x003   3	文件系统标志
	PACKED_BIOS_PARAMETER_BLOCK PackedBpb; // offset = 0x00B  11
	// Fat32特有
	ULONG bsBigSectorsPerFat; // total sectors per FAT		offset = 0x024 36	每个FAT表大小扇区数
	USHORT bsExtFlags;		  // extended flags				offset = 0x028 40	标记
	USHORT bsFS_Version;	  // filesystem version			offset = 0x02A 42	版本号
	ULONG bsRootDirStrtClus;  // first cluster of root dir	offset = 0x02C 44	根目录起始族号，通常为2号族
	USHORT bsFsInfoSec;		  // sector of FSInfo sec			offset = 0x030 48	FSINFO所在的扇区号，通常位于1号扇区
	USHORT bsBkUpBootSec;	  // sector of backup boot sec	offset = 0x032 50	备份引导扇区的位置，通常为6号扇区
	UCHAR bsReserved2[12];	  // reserved						offset = 0x034 52	未使用
	// fat共有
	UCHAR PhysicalDriveNumber; // offset = 0x040  64	BIOS int 13H 设备号 0x80
	UCHAR Reserved;			   // offset = 0x041  65	未使用
	UCHAR Signature;		   // offset = 0x042  66	扩展引导标志
	UCHAR Id[4];			   // offset = 0x043  67	卷序列号
	UCHAR VolumeLabel[11];	   // offset = 0x047  71	卷标
	UCHAR SystemId[8];		   // offset = 0x052  82	文件系统格式
	UCHAR BootStrap[420];	   // offset = 0x05A  90	引导代码
	USHORT bsSignature;		   // offset = 0x1FE, 510	签名(0xAA55h)
} PACKED_BOOT_SECTOR_FAT32;	   // sizeof = 0x200  512

typedef PACKED_BOOT_SECTOR_FAT32* PPACKED_BOOT_SECTOR_FAT32;

//
//  Define the boot sector
// D:\bak2\sample\win2k\private\ntos\fsrec\fat_rec.h

typedef struct _PACKED_BOOT_SECTOR
{
	UCHAR Jump[3];						   // offset = 0x000   0	汇编指令，跳转到引导代码处
	UCHAR Oem[8];						   // offset = 0x003   3	文件系统标志
	PACKED_BIOS_PARAMETER_BLOCK PackedBpb; // offset = 0x00B  11
	UCHAR PhysicalDriveNumber;			   // offset = 0x024  36	BIOS int 13H 设备号 0x80
	UCHAR Reserved;						   // offset = 0x025  37	未使用
	UCHAR Signature;					   // offset = 0x026  38	扩展引导标志
	UCHAR Id[4];						   // offset = 0x027  39	卷序列号
	UCHAR VolumeLabel[11];				   // offset = 0x02B  43	卷标
	UCHAR SystemId[8];					   // offset = 0x036  54	文件系统格式
	UCHAR BootStrap[448];				   // offset = 0x03E  62	引导代码
	USHORT bsSignature;					   // offset = 0x1FE, 510	签名(0xAA55h)
} PACKED_BOOT_SECTOR;					   // sizeof = 0x200  512

typedef PACKED_BOOT_SECTOR* PPACKED_BOOT_SECTOR;

// Fat32的目录项结构
typedef struct _DIRStruct_
{
	BYTE DIR_Name[11];					// 文件名 (偏移 0)
	BYTE DIR_Attr;						// 文件属性 (偏移 11)
	BYTE DIR_NTRes;						// 保留字段 (偏移 12)
	BYTE DIR_CrtTimeTenth;				// 创建时间的十分之一秒 (偏移 13)
	WORD DIR_CrtTime;					// 创建时间 (偏移 14)
	WORD DIR_CrtDate;					// 创建日期 (偏移 16)
	WORD DIR_LstAccDate;				// 最近一次访问日期 (偏移 18)
	WORD DIR_FstClusHI;					// 文件内容起始簇号的高两个字节 (偏移 20)
	WORD DIR_WrtTime;					// 最近一次写入时间 (偏移 22)
	WORD DIR_WrtDate;					// 最近一次写入日期 (偏移 24)
	WORD DIR_FstClusLO;					// 文件内容起始簇号的低两个字节 (偏移 26)
	DWORD DIR_FileSize;					// 文件大小 (偏移 28)
} DIRSTRUCT, * PDIRSTRUCT, * LPDIRSTRUCT; // 目录项结构体定义

// 族流结构
typedef struct tag_Area
{
	DWORD dwStart;
	DWORD dwSize;
} AREA;

typedef struct tag_ClusterList
{
	DWORD dwNumber;
	AREA Area[1];
} CLUSTERLIST, * PCLUSTERLIST, * LPCLUSTERLIST;

// 分区的唯一ID,存储在注册表的 System\\MountedDevices 里
typedef struct part_UniqueID
{
	DWORD dwDiskMark;	  // 磁盘签名
	DWORD64 dwDiskOffset; // 分区偏移扇区
} PARTITION_UNIQUE_ID, * PPARTITION_UNIQUE_ID;

// GPT类磁盘在注册表里的值
typedef struct part_GuidID
{
	DWORD64 dwDiskOffset; // 分区偏移扇区
	GUID dwDiskMark;	  // 磁盘签名
} PARTITION_GUID_ID, * PPARTITION_GUID_ID;

// http://www.cnblogs.com/qiuyi21/p/3569194.html
#ifndef GPT_HEADER_SIGNATURE
#define GPT_HEADER_SIGNATURE 0x5452415020494645LL	//"EFI PART"
#endif

typedef struct
{
	uint64_t signature;		   // 0		GPT 头部签名
	int revision;			   // 8		GPT 版本号
	int header_size;		   // 12	GPT 头部大小
	uint32_t crc_header;	   // 16	GPT 头部校验和
	int reserved;			   // 20	保留字段
	uint64_t lba_current;	   // 24	第一个 GPT 表的逻辑块地址
	uint64_t lba_backup;	   // 32	备份 GPT 表的逻辑块地址
	uint64_t lba_first_usable; // 40	可用的第一个分区的起始逻辑块地址
	uint64_t lba_last_usable;  // 48	可用的最后一个分区的结束逻辑块地址
	uint8_t disk_guid[16];	   // 56	磁盘的全局唯一标识符
	uint64_t lba_entry_start;  // 72	分区条目表的起始逻辑块地址
	int entry_count;		   // 80	分区条目表中的分区数量
	int entry_size;			   // 84	每个分区条目的大小
	uint32_t crc_entries;	   // 88	分区条目表的校验和
} gpt_header;				   // 92	GPT 头部结构体定义

#define IS_GUID_ZERO(guid) ((memcmp((guid), "\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00", 16)) == 0)

typedef struct
{
	uint8_t type_guid[16];	 // 0 分区类型 GUID
	uint8_t unique_guid[16]; // 16 分区唯一标识符 GUID
	uint64_t lba_first;		 // 32 分区起始逻辑块地址
	uint64_t lba_last;		 // 40 分区结束逻辑块地址
	uint64_t attribute;		 // 48 分区属性标志位, 位 0: 系统分区标志，磁盘分区工具应将其保留, 位 60: 只读标志, 位 62: 隐藏标志, 位 63: 不自动挂载标志（例如，不分配驱动器字母）
	uint16_t name[36];		 // 56 分区名称，UTF-16 编码
} partition_entry;			 // 128 分区条目结构体定义

typedef struct
{
	uint8_t status;			   // 0 分区状态
	uint8_t start_head;		   // 1 起始磁头号
	uint16_t start_sector;	   // 2 起始扇区号
	uint8_t part_type;		   // 4 分区类型
	uint8_t end_head;		   // 5 结束磁头号
	uint16_t end_sector;	   // 6 结束扇区号
	uint32_t first_abs_sector; // 8 分区的第一个绝对扇区号
	uint32_t sector_count;	   // 12 分区的扇区数
} mbr_part_entry;			   // 16 MBR（主引导记录）分区条目结构体定义

typedef struct
{
	uint8_t boot_code[440];				// 0 引导代码
	int unique_mbr_signature;			// 440 MBR 唯一标识符
	short unknown;						// 444 未知字段
	mbr_part_entry partition_record[4]; // 446 四个 MBR 分区条目
	uint16_t signature;					// 510 MBR 标志
} legacy_mbr;							// 512 传统 MBR（主引导记录）结构体定义

#pragma pack()

#ifndef VOLUMEINFO_DEFINE
#define VOLUMEINFO_DEFINE
typedef struct _VOLUMEINFO {
	DWORD DiskNumber = 0;
	DWORD64 StartingOffset = 0;
	DWORD64 ExtentLength = 0;
	std::wstring sVolumePath = _T("");
	std::wstring sVolumeShadowPath = _T("");
} VOLUMEINFO, * PVOLUMEINFO;
#endif //VOLUMEINFO_DEFINE
