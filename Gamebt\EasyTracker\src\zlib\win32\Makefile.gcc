# Makefile for zlib, derived from Makefile.dj2.
# Modified for mingw32 by <PERSON><PERSON>, 6/16/98.
# Updated for zlib 1.2.x by <PERSON> and <PERSON><PERSON><PERSON>, Mar-2003.
# Last updated: 1-Aug-2003.
# Tested under <PERSON><PERSON><PERSON> and <PERSON>GW.

# Copyright (C) 1995-2003 Jean<PERSON>lo<PERSON> Gail<PERSON>.
# For conditions of distribution and use, see copyright notice in zlib.h

# To compile, or to compile and test, type:
#
#   make -fmakefile.gcc;  make test testdll -fmakefile.gcc
#
# To use the asm code, type:
#   cp contrib/asm?86/match.S ./match.S
#   make LOC=-DASMV OBJA=match.o -fmakefile.gcc
#
# To install libz.a, zconf.h and zlib.h in the system directories, type:
#
#   make install -fmakefile.gcc

# Note:
# If the platform is *not* MinGW (e.g. it is Cygwin or UWIN),
# the DLL name should be changed from "zlib1.dll".

STATICLIB = libz.a
SHAREDLIB = zlib1.dll
IMPLIB    = libzdll.a

#LOC = -DASMV
#LOC = -DDEBUG -g

CC = gcc
CFLAGS = $(LOC) -O3 -Wall

AS = $(CC)
ASFLAGS = $(LOC) -Wall

LD = $(CC)
LDFLAGS = $(LOC) -s

AR = ar
ARFLAGS = rcs

RC = windres
RCFLAGS = --define GCC_WINDRES

CP = cp -fp
# If GNU install is available, replace $(CP) with install.
INSTALL = $(CP)
RM = rm -f

prefix = /usr/local
exec_prefix = $(prefix)

OBJS = adler32.o compress.o crc32.o deflate.o gzio.o infback.o \
       inffast.o inflate.o inftrees.o trees.o uncompr.o zutil.o
OBJA =

all: $(STATICLIB) $(SHAREDLIB) $(IMPLIB) example minigzip example_d minigzip_d

test: example minigzip
	./example
	echo hello world | ./minigzip | ./minigzip -d

testdll: example_d minigzip_d
	./example_d
	echo hello world | ./minigzip_d | ./minigzip_d -d

.c.o:
	$(CC) $(CFLAGS) -c -o $@ $<

.S.o:
	$(AS) $(ASFLAGS) -c -o $@ $<

$(STATICLIB): $(OBJS) $(OBJA)
	$(AR) $(ARFLAGS) $@ $(OBJS) $(OBJA)

$(IMPLIB): $(SHAREDLIB)

$(SHAREDLIB): win32/zlib.def $(OBJS) $(OBJA) zlibrc.o
	dllwrap --driver-name $(CC) --def win32/zlib.def \
	  --implib $(IMPLIB) -o $@ $(OBJS) $(OBJA) zlibrc.o
	strip $@

example: example.o $(STATICLIB)
	$(LD) $(LDFLAGS) -o $@ example.o $(STATICLIB)

minigzip: minigzip.o $(STATICLIB)
	$(LD) $(LDFLAGS) -o $@ minigzip.o $(STATICLIB)

example_d: example.o $(IMPLIB)
	$(LD) $(LDFLAGS) -o $@ example.o $(IMPLIB)

minigzip_d: minigzip.o $(IMPLIB)
	$(LD) $(LDFLAGS) -o $@ minigzip.o $(IMPLIB)

zlibrc.o: win32/zlib1.rc
	$(RC) $(RCFLAGS) -o $@ win32/zlib1.rc


# INCLUDE_PATH and LIBRARY_PATH must be set.

.PHONY: install uninstall clean

install: zlib.h zconf.h $(LIB)
	-@if not exist $(INCLUDE_PATH)/nul mkdir $(INCLUDE_PATH)
	-@if not exist $(LIBRARY_PATH)/nul mkdir $(LIBRARY_PATH)
	-$(INSTALL) zlib.h $(INCLUDE_PATH)
	-$(INSTALL) zconf.h $(INCLUDE_PATH)
	-$(INSTALL) $(STATICLIB) $(LIBRARY_PATH)
	-$(INSTALL) $(IMPLIB) $(LIBRARY_PATH)

uninstall:
	-$(RM) $(INCLUDE_PATH)/zlib.h
	-$(RM) $(INCLUDE_PATH)/zconf.h
	-$(RM) $(LIBRARY_PATH)/$(STATICLIB)
	-$(RM) $(LIBRARY_PATH)/$(IMPLIB)

clean:
	-$(RM) $(STATICLIB)
	-$(RM) $(SHAREDLIB)
	-$(RM) $(IMPLIB)
	-$(RM) *.o
	-$(RM) *.exe
	-$(RM) foo.gz

adler32.o: zlib.h zconf.h
compress.o: zlib.h zconf.h
crc32.o: crc32.h zlib.h zconf.h
deflate.o: deflate.h zutil.h zlib.h zconf.h
example.o: zlib.h zconf.h
gzio.o: zutil.h zlib.h zconf.h
inffast.o: zutil.h zlib.h zconf.h inftrees.h inflate.h inffast.h
inflate.o: zutil.h zlib.h zconf.h inftrees.h inflate.h inffast.h
infback.o: zutil.h zlib.h zconf.h inftrees.h inflate.h inffast.h
inftrees.o: zutil.h zlib.h zconf.h inftrees.h
minigzip.o: zlib.h zconf.h
trees.o: deflate.h zutil.h zlib.h zconf.h trees.h
uncompr.o: zlib.h zconf.h
zutil.o: zutil.h zlib.h zconf.h
