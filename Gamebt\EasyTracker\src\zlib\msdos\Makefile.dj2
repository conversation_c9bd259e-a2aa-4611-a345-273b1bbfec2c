# Makefile for zlib.  Modified for djgpp v2.0 by <PERSON><PERSON> <PERSON><PERSON>, 3/15/96.
# Copyright (C) 1995-1998 Jean-loup Gailly.
# For conditions of distribution and use, see copyright notice in zlib.h

# To compile, or to compile and test, type:
#
#   make -fmakefile.dj2;  make test -fmakefile.dj2
#
# To install libz.a, zconf.h and zlib.h in the djgpp directories, type:
#
#    make install -fmakefile.dj2
#
# after first defining LIBRARY_PATH and INCLUDE_PATH in djgpp.env as
# in the sample below if the pattern of the DJGPP distribution is to
# be followed.  Remember that, while <sp>'es around <=> are ignored in
# makefiles, they are *not* in batch files or in djgpp.env.
# - - - - -
# [make]
# INCLUDE_PATH=%\>;INCLUDE_PATH%%\DJDIR%\include
# LIBRARY_PATH=%\>;LIBRARY_PATH%%\DJDIR%\lib
# BUTT=-m486
# - - - - -
# Alternately, these variables may be defined below, overriding the values
# in djgpp.env, as
# INCLUDE_PATH=c:\usr\include
# LIBRARY_PATH=c:\usr\lib

CC=gcc

#CFLAGS=-MMD -O
#CFLAGS=-O -DMAX_WBITS=14 -DMAX_MEM_LEVEL=7
#CFLAGS=-MMD -g -DDEBUG
CFLAGS=-MMD -O3 $(BUTT) -Wall -Wwrite-strings -Wpointer-arith -Wconversion \
             -Wstrict-prototypes -Wmissing-prototypes

# If cp.exe is available, replace "copy /Y" with "cp -fp" .
CP=copy /Y
# If gnu install.exe is available, replace $(CP) with ginstall.
INSTALL=$(CP)
# The default value of RM is "rm -f."  If "rm.exe" is found, comment out:
RM=del
LDLIBS=-L. -lz
LD=$(CC) -s -o
LDSHARED=$(CC)

INCL=zlib.h zconf.h
LIBS=libz.a

AR=ar rcs

prefix=/usr/local
exec_prefix = $(prefix)

OBJS = adler32.o compress.o crc32.o gzio.o uncompr.o deflate.o trees.o \
       zutil.o inflate.o infback.o inftrees.o inffast.o

OBJA =
# to use the asm code: make OBJA=match.o

TEST_OBJS = example.o minigzip.o

all: example.exe minigzip.exe

check: test
test: all
	./example
	echo hello world | .\minigzip | .\minigzip -d

%.o : %.c
	$(CC) $(CFLAGS) -c $< -o $@

libz.a: $(OBJS) $(OBJA)
	$(AR) $@ $(OBJS) $(OBJA)

%.exe : %.o $(LIBS)
	$(LD) $@ $< $(LDLIBS)

# INCLUDE_PATH and LIBRARY_PATH were set for [make] in djgpp.env .

.PHONY : uninstall clean

install: $(INCL) $(LIBS)
	-@if not exist $(INCLUDE_PATH)\nul mkdir $(INCLUDE_PATH)
	-@if not exist $(LIBRARY_PATH)\nul mkdir $(LIBRARY_PATH)
	$(INSTALL) zlib.h $(INCLUDE_PATH)
	$(INSTALL) zconf.h $(INCLUDE_PATH)
	$(INSTALL) libz.a $(LIBRARY_PATH)

uninstall:
	$(RM) $(INCLUDE_PATH)\zlib.h
	$(RM) $(INCLUDE_PATH)\zconf.h
	$(RM) $(LIBRARY_PATH)\libz.a

clean:
	$(RM) *.d
	$(RM) *.o
	$(RM) *.exe
	$(RM) libz.a
	$(RM) foo.gz

DEPS := $(wildcard *.d)
ifneq ($(DEPS),)
include $(DEPS)
endif
