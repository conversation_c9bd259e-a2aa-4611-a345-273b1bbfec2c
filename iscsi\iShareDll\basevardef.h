﻿#pragma once

//使用##运算符来连接两个宏参数, PATH_SEP是一个根据不同平台定义的宏
#if (defined(_WIN32) || defined(_WIN64))
#define PATH_SEP "\\"
#define PATH_SEP_W L"\\"
#define PATH_SEP_T _T("\\")
#else
//gcc 下 ##连接符号只能连接两个标识符或者两个字符串常量，不能连接一个字符串常量和一个标识符， 可以用空格分隔，编译器会自动拼接
#define PATH_SEP "/"
#define PATH_SEP_W L"/"
#define PATH_SEP_T _T("/")
#endif

// 定义标准版路径拼接宏（char 字符串）， 输出 TCHAR 字符串
#define CONCAT_PATH_2(X, Y) _T(X PATH_SEP Y)
#define CONCAT_PATH_3(X, Y, Z) _T(X PATH_SEP Y PATH_SEP Z)

// 定义宽字符版路径拼接宏（wchar_t 字符串）， 输出 wchar_t 字符串
#define CONCAT_PATH_2_W(X, Y) X PATH_SEP_W Y
#define CONCAT_PATH_3_W(X, Y, Z) X PATH_SEP_W Y PATH_SEP_W Z

// 定义TCHAR版路径拼接宏（TCHAR 字符串）， 输出 TCHAR 字符串
#define CONCAT_PATH_2_T(X, Y) X PATH_SEP_T Y
#define CONCAT_PATH_3_T(X, Y, Z) X PATH_SEP_T Y PATH_SEP_T Z

#ifndef MAKELINK_INI_FILENAME
#define MAKELINK_INI_FILENAME_A "UserData.ini"
#define MAKELINK_INI_FILENAME _T(MAKELINK_INI_FILENAME_A)
#endif
#ifndef MAKELINK_INI_FOLDER
#define MAKELINK_INI_FOLDER_A "UserData" //个人转移文件存放目录
#define MAKELINK_INI_FOLDER _T(MAKELINK_INI_FOLDER_A) //个人转移文件存放目录
#endif //MAKELINK_INI_FOLDER
#ifndef MAKELINK_INI_PATH
#define MAKELINK_INI_PATH CONCAT_PATH_2(MAKELINK_INI_FOLDER_A, MAKELINK_INI_FILENAME_A) //个人转移文件存放目录
#endif //MAKELINK_INI_PATH
#ifndef USERSET_INI_PATH
#define USERSET_INI_PATH CONCAT_PATH_2(MAKELINK_INI_FOLDER_A, "UserSet.ini")
#endif //USERSET_INI_PATH

#ifndef MAKELINK_INI_SET_SECTION
#define MAKELINK_INI_ENV_SECTION _T("env")
#endif //MAKELINK_INI_SET_SECTION

#ifndef MAKELINK_INI_HIVE_SECTION
#define MAKELINK_INI_HIVE_SECTION _T("hive")
#endif //MAKELINK_INI_HIVE_SECTION

#ifndef MAKELINK_INI_ENV_TEMP
#define MAKELINK_INI_ENV_TEMP _T("%TEMP%")
#endif //MAKELINK_INI_ENV_TEMP

#ifndef MAKELINK_INI_ENV_TMP
#define MAKELINK_INI_ENV_TMP _T("%TMP%")
#endif //MAKELINK_INI_ENV_TMP

//userprofile
#ifndef MAKELINK_INI_ENV_USERPROFILE
#define MAKELINK_INI_ENV_USERPROFILE _T("%USERPROFILE%")
#endif //MAKELINK_INI_ENV_USERPROFILE

#ifndef MAKELINK_INI_ENV_CURRENT_SID
#define MAKELINK_INI_ENV_CURRENT_SID _T("%CurrentSID%")
#endif //MAKELINK_INI_ENV_CURRENT_SID

#ifndef MAKELINK_INI_FILTER_SECTION
#define MAKELINK_INI_FILTER_SECTION _T("filter")
#endif //MAKELINK_INI_FILTER_SECTION

#ifndef MAKELINK_INI_FILTER_APPLICATIONLAYER
#define MAKELINK_INI_FILTER_APPLICATIONLAYER _T("ApplicationLayer")
#endif //MAKELINK_INI_FILTER_APPLICATIONLAYER

//ApplicationLayerSuper
#ifndef MAKELINK_INI_FILTER_APPLICATIONLAYER_SUPER
#define MAKELINK_INI_FILTER_APPLICATIONLAYER_SUPER _T("ApplicationLayerSuper")
#endif //MAKELINK_INI_FILTER_APPLICATIONLAYER_SUPER

//bRegisterLayerDisable
#ifndef MAKELINK_INI_FILTER_REGISTERLAYER_DISABLE
#define MAKELINK_INI_FILTER_REGISTERLAYER_DISABLE _T("RegisterLayerDisable")
#endif //MAKELINK_INI_FILTER_REGISTERLAYER_DISABLE

//cUserDataDiskLetter
#ifndef MAKELINK_INI_FILTER_USERDATADISKLETTER
#define MAKELINK_INI_FILTER_USERDATADISKLETTER _T("UserDataDiskLetter")
#endif //MAKELINK_INI_FILTER_USERDATADISKLETTER

#ifndef MAKELINK_INI_FILTER_REGISTER_BLACK_DEFAULT
#define MAKELINK_INI_FILTER_REGISTER_BLACK_DEFAULT _T("RegisterBlackDefault")   //注册表项路径-黑名单默认
#endif //MAKELINK_INI_FILTER_REGISTER_BLACK_DEFAULT

#ifndef MAKELINK_INI_FILTER_REGISTER_BLACK
#define MAKELINK_INI_FILTER_REGISTER_BLACK _T("RegisterBlack")
#endif //MAKELINK_INI_FILTER_REGISTER_BLACK

#ifndef MAKELINK_INI_FILTER_REGISTER_WHITE_DEFAULT
#define MAKELINK_INI_FILTER_REGISTER_WHITE_DEFAULT _T("RegisterWhiteDefault")
#endif //MAKELINK_INI_FILTER_REGISTER_WHITE_DEFAULT

#ifndef MAKELINK_INI_FILTER_REGISTER_WHITE
#define MAKELINK_INI_FILTER_REGISTER_WHITE _T("RegisterWhite")
#endif //MAKELINK_INI_FILTER_REGISTER_WHITE

#ifndef MAKELINK_INI_FILTER_FILE_BLACK_DEFAULT
#define MAKELINK_INI_FILTER_FILE_BLACK_DEFAULT _T("FileBlackDefault")
#endif //MAKELINK_INI_FILTER_FILE_BLACK_DEFAULT

#ifndef MAKELINK_INI_FILTER_FILE_BLACK_SYSTEM
#define MAKELINK_INI_FILTER_FILE_BLACK_SYSTEM _T("FileBlackSystem")
#endif //MAKELINK_INI_FILTER_FILE_BLACK_SYSTEM

#ifndef MAKELINK_INI_FILTER_FILE_BLACK
#define MAKELINK_INI_FILTER_FILE_BLACK _T("FileBlack")
#endif //MAKELINK_INI_FILTER_FILE_BLACK

#ifndef MAKELINK_INI_FILTER_FILE_WHITE_DEFAULT
#define MAKELINK_INI_FILTER_FILE_WHITE_DEFAULT _T("FileWhiteDefault")
#endif //MAKELINK_INI_FILTER_FILE_WHITE_DEFAULT

#ifndef MAKELINK_INI_FILTER_FILE_WHITE_SYSTEM
#define MAKELINK_INI_FILTER_FILE_WHITE_SYSTEM _T("FileWhiteSystem")
#endif //MAKELINK_INI_FILTER_FILE_WHITE_SYSTEM

#ifndef MAKELINK_INI_FILTER_FILE_WHITE
#define MAKELINK_INI_FILTER_FILE_WHITE _T("FileWhite")
#endif //MAKELINK_INI_FILTER_FILE_WHITE

//在用户登录时，HKEY_CLASSES_ROOT 是由 HKEY_LOCAL_MACHINE\Software\Classes 和 HKEY_CURRENT_USER\Software\Classes 合并而成的
#ifndef MAKELINK_INI_HKEY_CLASSES_ROOT
#define MAKELINK_INI_HKEY_CLASSES_ROOT _T("HKEY_CLASSES_ROOT")
#endif //MAKELINK_INI_HKEY_CLASSES_ROOT

#ifndef REGISTRY_MACHINE_HKCR
#define REGISTRY_MACHINE_HKCR _T("\\Registry\\Machine\\Software\\Classes")
#endif //REGISTRY_MACHINE_HKCR

#ifndef MAKELINK_INI_HKEY_CURRENT_USER
#define MAKELINK_INI_HKEY_CURRENT_USER _T("HKEY_CURRENT_USER")
#endif //MAKELINK_INI_HKEY_CURRENT_USER

//\Registry\User\<当前用户的SID>
#ifndef REGISTRY_MACHINE_HKCU
#define REGISTRY_MACHINE_HKCU _T("\\Registry\\User\\%wZ")
#endif //REGISTRY_MACHINE_HKCU

#ifndef MAKELINK_INI_HKEY_CURRENT_USER_CLASSES
#define MAKELINK_INI_HKEY_CURRENT_USER_CLASSES _T("HKEY_CURRENT_USER\\Software\\Classes")
#endif //MAKELINK_INI_HKEY_CURRENT_USER_CLASSES

//\Registry\User\<当前用户的SID>_Classes  当用户注销后，用户特定的类信息会被存储在 HKEY_USERS\\<用户SID>_Classes 中
//这个键存储了与文件类型关联的配置信息以及 COM 类信息，允许用户自定义文件关联和 COM 类注册信息，以适应个人偏好或应用程序需求。
#ifndef REGISTRY_MACHINE_HKCU_CLASSES
//#define REGISTRY_MACHINE_HKCU_CLASSES _T("\\Registry\\User\\%wZ_Classes")
#define REGISTRY_MACHINE_HKCU_CLASSES _T("\\Registry\\User\\%wZ\\Software\\Classes")
#endif //REGISTRY_MACHINE_HKCU_CLASSES

//_Classes
#ifndef REGISTRY_MACHINE_HKCU_CLASSES_NAME
#define REGISTRY_MACHINE_HKCU_CLASSES_NAME _T("_Classes")
#endif //REGISTRY_MACHINE_HKCU_CLASSES_NAME

//HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\ProfileList
#ifndef REGISTRY_MACHINE_HKLM_PROFILELIST
#define REGISTRY_MACHINE_HKLM_PROFILELIST _T("\\Registry\\Machine\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\ProfileList")
#endif //REGISTRY_MACHINE_HKLM_PROFILELIST

//ProfileImagePath
#ifndef REGISTRY_MACHINE_HKLM_PROFILELIST_PROFILE_IMAGE_PATH
#define REGISTRY_MACHINE_HKLM_PROFILELIST_PROFILE_IMAGE_PATH _T("ProfileImagePath")
#endif //REGISTRY_MACHINE_HKLM_PROFILELIST_PROFILE_IMAGE_PATH

#ifndef MAKELINK_INI_HKEY_LOCAL_MACHINE
#define MAKELINK_INI_HKEY_LOCAL_MACHINE _T("HKEY_LOCAL_MACHINE")
#endif //MAKELINK_INI_HKEY_LOCAL_MACHINE

#ifndef REGISTRY_MACHINE_HKLM
#define REGISTRY_MACHINE_HKLM _T("\\Registry\\Machine")
#endif //REGISTRY_MACHINE_HKLM

#ifndef MAKELINK_INI_HKEY_LOCAL_MACHINE_CURRENTCONTROLSET
#define MAKELINK_INI_HKEY_LOCAL_MACHINE_CURRENTCONTROLSET _T("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet")
#endif //MAKELINK_INI_HKEY_LOCAL_MACHINE_CURRENTCONTROLSET

#ifndef REGISTRY_MACHINE_SYSTEM_CURRENTCONTROLSET
#define REGISTRY_MACHINE_SYSTEM_CURRENTCONTROLSET _T("\\Registry\\Machine\\System\\CurrentControlSet")
#endif //REGISTRY_MACHINE_SYSTEM_CURRENTCONTROLSET

#ifndef REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ANSI
#define REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ANSI "\\Registry\\Machine\\System\\ControlSet%03d"
#endif //REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ANSI

#ifndef REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET
#define REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET _T(REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ANSI)
#endif //REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET

#ifndef REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_PATH
#define REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_PATH _T("\\Registry\\Machine\\SYSTEM\\Select")
#endif //REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_PATH

#ifndef REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_VALUE_NAME
#define REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_VALUE_NAME _T("Current")
#endif //REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_VALUE_NAME

#ifndef MAKELINK_INI_HKEY_LOCAL_MACHINE_CURRENT_HARDWARE
#define MAKELINK_INI_HKEY_LOCAL_MACHINE_CURRENT_HARDWARE _T("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Hardware Profiles\\Current")
#endif //MAKELINK_INI_HKEY_LOCAL_MACHINE_CURRENT_HARDWARE

#ifndef REGISTRY_MACHINE_SYSTEM_CURRENTCONTROLSET_HARDWAREPROFILES_CURRENT
#define REGISTRY_MACHINE_SYSTEM_CURRENTCONTROLSET_HARDWAREPROFILES_CURRENT _T("\\Registry\\Machine\\System\\CurrentControlSet\\Hardware Profiles\\Current")
#endif //REGISTRY_MACHINE_SYSTEM_CURRENTCONTROLSET_HARDWAREPROFILES_CURRENT

#ifndef REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE
#define REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE CONCAT_PATH_2(REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ANSI, "Hardware Profiles\\%04d")
#endif //REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE

#ifndef REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE_CONFIG_PATH
#define REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE_CONFIG CONCAT_PATH_2(REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ANSI, "Control\\IDConfigDB")
#endif //REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE_CONFIG_PATH

#ifndef REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE_CONFIG_VALUE_NAME
#define REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE_CONFIG_VALUE_NAME _T("CurrentConfig")
#endif //REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE_CONFIG_VALUE_NAME

#ifndef REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_HIVELIST
#define REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_HIVELIST CONCAT_PATH_2(REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ANSI, "Control\\hivelist")
#endif //REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_HIVELIST

#ifndef HKEY_LOCAL_MACHINE_CURRENTCONTROLSET_HIVELIST
#define HKEY_LOCAL_MACHINE_CURRENTCONTROLSET_HIVELIST _T("SYSTEM\\CurrentControlSet\\Control\\hivelist")
#endif //HKEY_LOCAL_MACHINE_CURRENTCONTROLSET_HIVELIST

#ifndef REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT
#define REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT CONCAT_PATH_2(REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ANSI, "Control\\Session Manager\\Environment")
#endif //REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT

#ifndef REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT_TEMP
#define REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT_TEMP _T("Temp")
#endif //REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT_TEMP

#ifndef REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT_TMP
#define REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT_TMP _T("Tmp")
#endif //REGISTRY_MACHINE_HKLM_CURRENTCONTROLSET_ENVIRONMENT_TMP

#ifndef MAKELINK_INI_HKEY_USERS
#define MAKELINK_INI_HKEY_USERS _T("HKEY_USERS")
#endif //MAKELINK_INI_HKEY_USERS

#ifndef REGISTRY_MACHINE_HKU
#define REGISTRY_MACHINE_HKU _T("\\Registry\\User")
#endif //REGISTRY_MACHINE_HKU

//\\Registry\\User\\S-1-5-18 和 \\Registry\\User\\.DEFAULT 是不同的注册表项
//\\Registry\\User\\.DEFAULT：这个路径指向 默认用户配置文件 的注册表项。默认用户配置文件是用作新用户配置文件的模板
#ifndef MAKELINK_INI_HKEY_USERS_DEFAULT
#define MAKELINK_INI_HKEY_USERS_DEFAULT _T("HKEY_USERS\\.DEFAULT")
#endif //MAKELINK_INI_HKEY_USERS_DEFAULT

//.DEFAULT
#ifndef REGISTRY_MACHINE_USER_DEFAULT
#define REGISTRY_MACHINE_USER_DEFAULT _T(".DEFAULT")
#endif //REGISTRY_MACHINE_USER_DEFAULT

//\Registry\User\S-1-5-18 这个路径指向 系统帐户（NT AUTHORITY\\SYSTEM）的注册表项
#ifndef REGISTRY_MACHINE_HKU_DEFAULT
#define REGISTRY_MACHINE_HKU_DEFAULT _T("\\Registry\\User\\S-1-5-18")
#endif //REGISTRY_MACHINE_HKU_DEFAULT

//S-1-5-18 S-1-5-19 S-1-5-20
#ifndef REGISTRY_MACHINE_USER_SID_SYSTEM
#define REGISTRY_MACHINE_USER_SID_SYSTEM _T("S-1-5-18")
#endif //REGISTRY_MACHINE_USER_SID_SYSTEM

#ifndef REGISTRY_MACHINE_USER_SID_LOCAL_SERVICE
#define REGISTRY_MACHINE_USER_SID_LOCAL_SERVICE _T("S-1-5-19")
#endif //REGISTRY_MACHINE_USER_SID_LOCAL_SERVICE

#ifndef REGISTRY_MACHINE_USER_SID_NETWORK_SERVICE
#define REGISTRY_MACHINE_USER_SID_NETWORK_SERVICE _T("S-1-5-20")
#endif //REGISTRY_MACHINE_USER_SID_NETWORK_SERVICE

#ifndef MAKELINK_INI_HKEY_CURRENT_CONFIG
#define MAKELINK_INI_HKEY_CURRENT_CONFIG _T("HKEY_CURRENT_CONFIG")
#endif //MAKELINK_INI_HKEY_CURRENT_CONFIG

//https://learn.microsoft.com/zh-cn/previous-versions/windows/it-pro/windows-server-2003/cc784153(v=ws.10)
//要确定硬件配置文件中的哪个编号子项表示当前硬件配置文件，请参阅 HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\IDConfigDB 中的 CurrentConfig 条目。CurrentConfig 的值对应于包含当前硬件配置文件的子项的编号。
#ifndef REGISTRY_MACHINE_HKCC_CURRENT
#define REGISTRY_MACHINE_HKCC_CURRENT REGISTRY_MACHINE_HKLM_CURRENT_HARDWARE
#endif //REGISTRY_MACHINE_HKCC_CURRENT

#ifndef MAKELINK_INI_ENV_SYSTEMROOT
#define MAKELINK_INI_ENV_SYSTEMROOT _T("%SYSTEMROOT%")
#endif