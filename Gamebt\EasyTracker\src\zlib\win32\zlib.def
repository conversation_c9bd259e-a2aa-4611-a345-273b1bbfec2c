LIBRARY
; zlib data compression library

EXPORTS
; basic functions
    zlibV<PERSON><PERSON>
    deflate
    deflateEnd
    inflate
    inflateEnd
; advanced functions
    deflateSetDictionary
    deflateCopy
    deflateReset
    deflateParams
    deflateBound
    deflatePrime
    inflateSetDictionary
    inflateSync
    inflateCopy
    inflateReset
    inflateBack
    inflateBackEnd
    zlibCompileFlags
; utility functions
    compress
    compress2
    compressBound
    uncompress
    gzopen
    gzdopen
    gzsetparams
    gzread
    gzwrite
    gzprintf
    gzputs
    gzgets
    gzputc
    gzgetc
    gzungetc
    gzflush
    gzseek
    gzrewind
    gztell
    gzeof
    gzclose
    gzerror
    gzclearerr
; checksum functions
    adler32
    crc32
; various hacks, don't look :)
    deflateInit_
    deflateInit2_
    inflateInit_
    inflateInit2_
    inflateBackInit_
    inflateSyncPoint
    get_crc_table
    zError
