STRPGMEXP PGMLVL(*CURRENT) SIGNATURE('ZLIB')

/*@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@*/
/*   Version 1.1.3 entry points.                                    */
/*@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@*/

/********************************************************************/
/*   *MODULE      ADLER32      ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("adler32")

/********************************************************************/
/*   *MODULE      COMPRESS     ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("compress")
  EXPORT SYMBOL("compress2")

/********************************************************************/
/*   *MODULE      CRC32        ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("crc32")
  EXPORT SYMBOL("get_crc_table")

/********************************************************************/
/*   *MODULE      DEFLATE      ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("deflate")
  EXPORT SYMBOL("deflateEnd")
  EXPORT SYMBOL("deflateSetDictionary")
  EXPORT SYMBOL("deflateCopy")
  EXPORT SYMBOL("deflateReset")
  EXPORT SYMBOL("deflateParams")
  EXPORT SYMBOL("deflatePrime")
  EXPORT SYMBOL("deflateInit_")
  EXPORT SYMBOL("deflateInit2_")

/********************************************************************/
/*   *MODULE      GZIO         ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("gzopen")
  EXPORT SYMBOL("gzdopen")
  EXPORT SYMBOL("gzsetparams")
  EXPORT SYMBOL("gzread")
  EXPORT SYMBOL("gzwrite")
  EXPORT SYMBOL("gzprintf")
  EXPORT SYMBOL("gzputs")
  EXPORT SYMBOL("gzgets")
  EXPORT SYMBOL("gzputc")
  EXPORT SYMBOL("gzgetc")
  EXPORT SYMBOL("gzflush")
  EXPORT SYMBOL("gzseek")
  EXPORT SYMBOL("gzrewind")
  EXPORT SYMBOL("gztell")
  EXPORT SYMBOL("gzeof")
  EXPORT SYMBOL("gzclose")
  EXPORT SYMBOL("gzerror")

/********************************************************************/
/*   *MODULE      INFLATE      ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("inflate")
  EXPORT SYMBOL("inflateEnd")
  EXPORT SYMBOL("inflateSetDictionary")
  EXPORT SYMBOL("inflateSync")
  EXPORT SYMBOL("inflateReset")
  EXPORT SYMBOL("inflateInit_")
  EXPORT SYMBOL("inflateInit2_")
  EXPORT SYMBOL("inflateSyncPoint")

/********************************************************************/
/*   *MODULE      UNCOMPR      ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("uncompress")

/********************************************************************/
/*   *MODULE      ZUTIL        ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("zlibVersion")
  EXPORT SYMBOL("zError")

/*@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@*/
/*   Version 1.2.1 additional entry points.                         */
/*@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@*/

/********************************************************************/
/*   *MODULE      COMPRESS     ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("compressBound")

/********************************************************************/
/*   *MODULE      DEFLATE      ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("deflateBound")

/********************************************************************/
/*   *MODULE      GZIO         ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("gzungetc")
  EXPORT SYMBOL("gzclearerr")

/********************************************************************/
/*   *MODULE      INFBACK      ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("inflateBack")
  EXPORT SYMBOL("inflateBackEnd")
  EXPORT SYMBOL("inflateBackInit_")

/********************************************************************/
/*   *MODULE      INFLATE      ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("inflateCopy")

/********************************************************************/
/*   *MODULE      ZUTIL        ZLIB         01/02/01  00:15:09      */
/********************************************************************/

  EXPORT SYMBOL("zlibCompileFlags")

ENDPGMEXP
