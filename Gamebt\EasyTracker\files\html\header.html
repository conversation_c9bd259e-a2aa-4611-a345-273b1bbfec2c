


<!-- HEADER.HTML
     WRITTEN BY TRINITY OF ZIONMATRIX -->
     
<!-- NOTES:

     This document can be edited/modified using any standard text editor,
     such as NOTEPAD.EXE (which comes with Windows). However, I suggest that
     you download a text-editor that is specifically designed for editing code.
     Most code editors will have added features, such as Line Numbers and Code
     Highlighting, both of which make it easier to read/edit HTML.

     My personal favorite is Aedix Suite...
     which can be freely downloaded at http://www.kt2k.com/download.php
     
     PLEASE NOTE: This document can be modified WHILE your tracker is running,
     however changes to this document will not be displayed until the tracker's
     refresh interval has passed (or the tracker has been restarted). The
     interval is specified using the "bnbt_refresh_static_interval" config key.
     
     You should NOT include <html>, <head> and/or <body> tags in this document.
     They are automatically generated by the tracker.
     
     You should NOT include stylesheet code in this document, unless you want to
     specify styles that will override your default stylesheet. Style specs listed
     in this document will only affect INDEX.HTML.
     
     This document is displayed ABOVE your torrent table. -->

<!-- BEGIN HEADER -->

<br><br> <!-- use these line breaks when using the "fixed-style" navigation bar -->

<center> <!-- START CENTERING PAGE ELEMENTS -->

<!-- you should not use <h3> tags when using the "fixed-style" navigation bar
because otherwise, your tracker's title will be oddly positioned under the navbar -->
                                      
  <h4>The Title of My Tracker</h4>

<script language="javascript" type="text/javascript"> // JAVASCRIPTS

// These variables specify custom labels for objects/text that appear on index.html
// For them to be used, "bnbt_use_custom_labels" must be set to 1 in your configuration
// If you uncomment a value ( by removing the // ), it becomes active.
// Please be sure not to activate a variable without setting its value.

             // SYNTAX: var variableName = "value";

// var labelSearch = "";

   /* the above variable sets the text that appears before the torrent search field in
    * the navigation bar and at the bottom of the page
    */

// var labelPageNumPrefix = "";
var labelPageNavPrefix = "Go To Page: ";

   /* the above variables set the string of text that appears before
    * the page numbers at the top and the page numbers at the bottom
    * respectively.
    */
    
// var buttonClearFilter = "";

   /* buttonClearFilter - the text that appears on the Clear Filter button
    * above the tag filter images
    */
    
// var buttonGo = "";
// var buttonClearSearch = "";

   /* the above variables set the text that appears on the Go and Clear Search
    * buttons that appear at the bottom of index.html
    */

// var linkBottomOfPage = "";

   /* the above variable sets the text to be used for the "Bottom of Page" link
    * that will take you to the search form and page navigation links at the
    * bottom of the page
    */
    
var linkA = "<img src=\"/files/images/other/triangle_up.gif\" border=\"0\">";
var linkZ = "<img src=\"/files/images/other/triangle_down.gif\" border=\"0\">";

   /* The above variables set the text for the ascending and descending sort links
    *
    * You will see that in this example, I have specified image files as opposed to text
    * You will also see that because the src and border values are typically surrounded
    * by quotation marks "..." that I had to use the escape character '\' to place the
    * quotation marks... this holds true for all JavaScript.
    *
    * Here are some ALTERNATIVES...
    */
    
// var linkA = "&uarr;"; /* Alternative 1 */
// var linkZ = "&darr;"; /* Alternative 1 */

// var linkA = "^";       /* Alternative 2 */
// var linkZ = "\u2228";  /* Alternative 2 */

var linkDownload = "torrent";
// var linkInfoLink = "";
var linkDelete = "DEL";

   /* the above variables set the text for the links that appear in the
    * Torrent (Download), InfoLink and Admin columns.
    */
    
/* The next set of variables specify the text to be used in each column header */

var headerTag = "Type";
// var headerInfoHash = "";
var headerName = "Torrent Name";
var headerTorrent = "Download";
var headerComments = "Msg";
// var headerAdded = "";
// var headerSize = "";
// var headerFiles = "";
var headerSeeders = "U/L";
var headerLeechers = "D/L";
var headerCompleted = "Done";
// var headerTransferred = "";

// var headerMinProgress = "";
// var headerAverageProgress = "";
// var headerMaxProgress = "";

// var headerMinLeft = "";
// var headerAverageLeft = "";
// var headerMaxLeft = "";

// var headerUploader = "";
// var headerInfoLink = "";
var headerAdmin = "DEL";

// ----------------------------------------------------------- END OF VARIABLES

// The next line specifies the DEFAULT window status that will appear in your browser.
window.defaultStatus = "POWERED BY The Trinity Edition of BNBT";

</script>

<!-- NOTE: It is no longer necessary to use JavaScript to set the browser's title
     bar value. This can now be set using the "bnbt_tracker_title" config key -->
     
<!-- OLD NAVIGATION LINKS:
     The following code will place links on your main page (INDEX.HTML) to
     reach the other pages of the tracker.
     
     IF YOU ARE USING THE NAVIGATION BAR, You should not use this code.
     You will see, by default, that the following code is commented out.
     To enable it, just remove the comment tags that appear before and after
     the code. -->
     
<!-- REMOVE THIS LINE TO ACTIVATE THE LINKS BELOW:

    <p class="navlinks">
    <a href="/login.html">Login</a> |
    <a href="/signup.html">Sign Up</a> |
    <a href="/login.html">My Torrents</a> |
    <a href="upload.html">Upload</a> |
    <a href="/info.html">Tracker Info</a> |
    <a href="/admin.html">Admin</a> |
    <a href="/users.html">Users</a>
    </p>
    
    REMOVE THIS LINE TO ACTIVATE THE LINKS ABOVE -->






<!-- DO NOT MODIFY PAST THIS LINE / ONLY ADD CODE ABOVE THIS LINE

  -- TORRENT TABLE STARTS HERE / END OF HEADER.HTML -->




