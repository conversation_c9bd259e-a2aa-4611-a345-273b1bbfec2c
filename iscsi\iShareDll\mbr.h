#pragma once
#include "linux_type.hpp"
#define LBA_SIZE_2TB ((DWORD64)0xFFFFFFFF)
#ifndef SECSIZE
#define SECSIZE (0x200) //默认扇区大小
#endif
#ifndef MBRSIGN
#define MBRSIGN (0xAA55)
#endif
#ifndef DBR_SIZE
#define DBR_SIZE (512)
#endif
#ifdef OEM_HAPPYSHARE_UPDATE
//为了单独对游戏更新机设置盘符而确定的安全位置
#define CLIENTLETTER_OFFSET (DBR_SIZE-68)
#else
#define CLIENTLETTER_OFFSET (DBR_SIZE-3)
#endif

//NTFS相关
#define NTFS_DBR_NAME "NTFS"
#define FAT32_DBR_NAME "FAT32"
//分区类型
#define PARTITION_TYPE_UNUSED 0x00
#define PARTITION_TYPE_EXTEND 0x05
#define PARTITION_TYPE_EXTENDWIN95 0x0F
#define PARTITION_TYPE_NTFS 0x07
#define PARTITION_TYPE_NTFSHIDDEN 0x17
#define PARTITION_TYPE_NTFS_LBA 0x27		   // NTFS文件系统（使用LBA寻址）
#define PARTITION_TYPE_MICROSOFT_RESERVED_HIDE 0x27
#define PARTITION_TYPE_FAT32 0x0B
#define PARTITION_TYPE_FAT32WIN95 0x0C
#define PARTITION_TYPE_FAT32HIDDEN 0x1B
#define PARTITION_TYPE_FAT32WIN95HIDDEN 0x1C

#pragma pack( 1 )

typedef struct tag_PtItem
{
	BYTE	bState;
	BYTE	bStartHead;
	BYTE	bStartSec;
	BYTE	bStartCyl;
	BYTE	bType;
	BYTE	bEndHead;
	BYTE	bEndSec;
	BYTE	bEndCyl;
	DWORD	dwStartSec;
	DWORD	dwTotalSecs;
}PTITEM, * LPPTITEM;

typedef struct _mbr
{
	BYTE	bootcode[0x1BE];
	PTITEM	bsPartEnts[4];
	WORD	bsSignature;			// boot block signature (0xAA55h)
}MBR, * PMBR;

typedef struct _PartEntry_
{
	BYTE	peBootable;				// 80h = bootable, 00h = not
	BYTE	peBeginHead;			// beginning head
	BYTE	peBeginSector;			// beginning sector
	BYTE	peBeginCylinder;		// beginning cylinder
	BYTE	peFileSystem;			// ID of filesystem
	BYTE	peEndHead;				// ending head
	BYTE	peEndSector;			// ending sector
	BYTE	peEndCylinder;			// ending cylinder
	DWORD	peStartSector;			// starting sector
	DWORD	peSectors;				// total sectors
}PartEntry;

typedef struct _BootSectorFat32_
{
	BYTE		bsJump[3];				// jmp to bootstrap instruction 0
	char		bsOemName[8];			// OEM name and version			3

	WORD		bsBytesPerSec;			// bytes per sector				11
	BYTE		bsSecPerClust;			// sectors per cluster			13
	WORD		bsResSectors;			// num reserved sectors			14
	BYTE		bsFats;					// num file allocation tables	16
	WORD		bsRootDirEnts;			// num root-directory entries	17
	WORD		bsSectors;				// total num secs (<= 32 megs)	19
	BYTE		bsMedia;				// media descriptor				21
	WORD		bsFatSecs;				// num sectors per FAT			22
	WORD		bsSecPerTrack;			// sectors per track (head)		24
	WORD		bsHeads;				// num heads					26
	DWORD		bsHiddenSecs;			// num hidden sectors			28
	DWORD		bsHugeSectors;			// total num sectors (> 32 megs)32
	DWORD		bsBigSectorsPerFat;		// total sectors per FAT		36
	WORD		bsExtFlags;				// extended flags				40
	WORD		bsFS_Version;			// filesystem version			42
	DWORD		bsRootDirStrtClus;		// first cluster of root dir	44
	WORD		bsFsInfoSec;			// sector of FSInfo sec			48
	WORD		bsBkUpBootSec;			// sector of backup boot sec	50
	WORD		bsReserved2[6];			// reserved						52

	BYTE		bsDriveNumber;			// drive number (80h)			64
	BYTE		bsReserved1;			// reserved						65
	BYTE		bsBootSignature;		// extended boot signature (29h)66
	DWORD		bsVolumeId;				// volume ID number				67
	char		bsVolumeLabel[11];		// volume label					71
	char		bsFileSysType[8];		// filesystem type				82
	BYTE		padding1[356];			// padding to fill out first sec90
	PartEntry	bsPartEnts[4];			// partition entry table		446
	WORD		bsSignature;			// boot block signature (0xAA55h)510
}BOOTSECTORFAT32, * PBOOTSECTORFAT32;

typedef struct	tag_NTFSBOOT
{
	BYTE			bJmp[3];            // 0
	BYTE			bNTFlags[4];		// 3  // like 'N','T','F','S'
	BYTE			bReserve1[4];       // 7
	WORD			wBytePerSector;     // b
	BYTE			bSectorPerCluster;  // d
	WORD			wReserveSectors;    // e
	BYTE			bFatNum;			// 0x10 // alway is 0
	WORD			wRootDirNum;		// 0x11 // alway is 0
	WORD			wSectorOfParti;     // 0x13 // alway is 0
	BYTE			bMedium;            // 0x15
	WORD			wSectorPerFat;		// 0x16 // alway is 0
	WORD			wSectorPerTrack;    // 0x18
	WORD			wHeadNum;           // 0x1a
	DWORD			dwHideSector;       // 0x1c
	DWORD			dwUnParti;     // 0x20 // alway is 0
	BYTE			bDeviceFlag;        //0x24
	BYTE			bReserve2;          //0x25
	WORD			wReserve3;          //0x26
	DWORD			ulSectorsOfParti; //0x28 all sectors - 1
	DWORD			ulSectorsOfPartiHigh; //0x2c all sectors - 1
	DWORD			ulMFTAddr;            //0x30
	DWORD			ulMFTAddrHigh;        //0x34
	DWORD			ulMFTMirrAddr;        //0x38
	DWORD			ulMFTMirrAddrHigh;    //0x3c
	char			bClusterPerFile;  //0x40
	BYTE			bReserve4[3];     //0x41
	char			bClusterPerINDX;  //0x44
	BYTE			bReserve5[3];     //0x45
	BYTE			bSerialID[8];     //0x48
	DWORD			bChecksum;		//0x50
	BYTE			cBootCode[426];	//0x54  boot code
	USHORT			bsSignature;	//0x1FE boot block signature (0xAA55h)
} NTFSBOOT, * PNTFSBOOT, * LPNTFSBOOT;

#pragma pack()
