﻿#include "stdafx.h"
#include <windows.h>
#include <sddl.h>
#include <vector>
#include "PrivilegeSystem.h"

//测试当前进程是否有某个权限
BOOL CheckPrivilege(LPCTSTR Privilege)
{
	HANDLE hToken(NULL);
	if (!OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &hToken))
	{
		return FALSE;
	}

	LUID luid;
	if (!LookupPrivilegeValue(NULL, Privilege, &luid))
	{
		CloseHandle(hToken);
		return FALSE;
	}

	PRIVILEGE_SET privs;
	privs.PrivilegeCount = 1;
	privs.Control = PRIVILEGE_SET_ALL_NECESSARY;
	privs.Privilege[0].Luid = luid;
	privs.Privilege[0].Attributes = SE_PRIVILEGE_ENABLED;

	BOOL bResult = FALSE;
	if (!PrivilegeCheck(hToken, &privs, &bResult))
	{
		CloseHandle(hToken);
		return FALSE;
	}

	CloseHandle(hToken);
	return bResult;
}

BOOL SetPrivilege(
	LPCTSTR Privilege,      // Privilege to enable/disable
	BOOL bEnablePrivilege   // TRUE to enable.  FALSE to disable
)
{
	HANDLE hToken(NULL);
	if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hToken))
	{
		if (GetLastError() == ERROR_NO_TOKEN)
		{
			if (!ImpersonateSelf(SecurityImpersonation))
			{
				return FALSE;
			}

			if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hToken))
			{
				return FALSE;
			}
		}
		else
		{
			return FALSE;
		}
	}

	TOKEN_PRIVILEGES tp = { 0 };
	LUID luid = { 0 };
	TOKEN_PRIVILEGES tpPrevious = { 0 };
	DWORD cbPrevious = sizeof(TOKEN_PRIVILEGES);

	if (!LookupPrivilegeValue(NULL, Privilege, &luid))
	{
		CloseHandle(hToken);
		return FALSE;
	}

	//
	// first pass.  get current privilege setting
	//
	tp.PrivilegeCount = 1;
	tp.Privileges[0].Luid = luid;
	tp.Privileges[0].Attributes = 0;

	BOOL ret = AdjustTokenPrivileges(hToken,
		FALSE,
		&tp,
		sizeof(TOKEN_PRIVILEGES),
		&tpPrevious,
		&cbPrevious);
	if (!ret || GetLastError() != ERROR_SUCCESS)
	{
		CloseHandle(hToken);
		return FALSE;
	}

	//
	// second pass.  set privilege based on previous setting
	//
	tpPrevious.PrivilegeCount = 1;
	tpPrevious.Privileges[0].Luid = luid;

	if (bEnablePrivilege) {
		tpPrevious.Privileges[0].Attributes |= (SE_PRIVILEGE_ENABLED);
	}
	else {
		tpPrevious.Privileges[0].Attributes ^= (SE_PRIVILEGE_ENABLED &
			tpPrevious.Privileges[0].Attributes);
	}

	ret = AdjustTokenPrivileges(hToken,
		FALSE,
		&tpPrevious,
		cbPrevious,
		NULL,
		NULL);

	if (!ret || GetLastError() != ERROR_SUCCESS)
	{
		CloseHandle(hToken);
		return FALSE;
	}

	CloseHandle(hToken);
	return TRUE;
}

// BOOL EnablePrivilege( LPCTSTR Privilege )
// {
// 	BOOL result(FALSE);
// 	HANDLE hToken;
// 	LUID luid;
// 	if(OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY,  &hToken)
// 		&& LookupPrivilegeValue( NULL, Privilege, &luid ) )
// 	{
// 		TOKEN_PRIVILEGES tp;
// 		tp.PrivilegeCount           = 1;
// 		tp.Privileges[0].Luid       = luid;
// 		tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;
// 		BOOL ret = AdjustTokenPrivileges(  hToken,
// 			FALSE,
// 			&tp,
// 			sizeof(tp),
// 			NULL,
// 			NULL );
// 		if ( ret && GetLastError() == ERROR_SUCCESS  )
// 			result = TRUE;
// 		CloseHandle(hToken);
// 	}
// 	return result;
// }

// BOOL IsAdministrator()
// {
// 	HANDLE                   hAccessToken;
// 	BYTE                     *InfoBuffer;
// 	PTOKEN_GROUPS            ptgGroups;
// 	DWORD                    dwInfoBufferSize;
// 	PSID                     psidAdministrators;
// 	SID_IDENTIFIER_AUTHORITY siaNtAuthority = SECURITY_NT_AUTHORITY;
// 	UINT                     i;
// 	BOOL                     bRet = FALSE;
//
// 	//第一步：调用OpenProcessToken函数获取当前进程TOKEN的句柄
// 	if(!OpenProcessToken(GetCurrentProcess(),TOKEN_QUERY,&hAccessToken))
// 		goto cleanup;
//
// 	InfoBuffer = new BYTE[1024];
// 	if(!InfoBuffer)
// 		goto cleanup;
//
// 	//当获得TOKEN句柄之后，我们还得获取这个TOKEN的分组信息：
// 	bRet = GetTokenInformation(hAccessToken,
// 		TokenGroups,
// 		InfoBuffer,
// 		1024,
// 		&dwInfoBufferSize);
//
// 	CloseHandle( hAccessToken );
//
// 	if(!bRet)
// 		goto cleanup;
//
// 	//第二步：为Local Admin账号获取安全标示符（SID），我们要利用这个账号在所有TOKEN分组中查找Local Admin SID（不要忘了调用FreeSid函数来释放内存）。
// 	if(!AllocateAndInitializeSid(&siaNtAuthority,
// 		2,
// 		SECURITY_BUILTIN_DOMAIN_RID,
// 		DOMAIN_ALIAS_RID_ADMINS,
// 		0,0,0,0,0,0,
// 		&psidAdministrators))
// 		goto cleanup;
//
// 	bRet = FALSE;
//
// 	//最后一步：在所有TOKEN分组中查找Local Admin SID：
// 	ptgGroups = (PTOKEN_GROUPS)InfoBuffer;
//
// 	for(i=0;i<ptgGroups->GroupCount;i++)
// 	{
// 		if(EqualSid(psidAdministrators,ptgGroups->Groups[i].Sid))
// 		{
// 			// 此进程有管理权限
// 			bRet = TRUE;
// 			break;
// 		}
// 	}
//
// 	FreeSid(psidAdministrators);
//
// cleanup:
//
// 	if (InfoBuffer)
// 		delete InfoBuffer;
//
// 	return bRet;
// }

BOOL SetProcessWorkingSet(SIZE_T minsize, SIZE_T maxsize)
{
	if (SetPrivilege(SE_INC_WORKING_SET_NAME, TRUE)) {
		return SetProcessWorkingSetSize(GetCurrentProcess(), minsize, maxsize);//设置程序的可用内存
		//SetProcessWorkingSetSizeEx(GetCurrentProcess(), minsize, maxsize,  QUOTA_LIMITS_HARDWS_MIN_ENABLE|QUOTA_LIMITS_HARDWS_MAX_DISABLE);//设置程序的可用内存
	}
	return FALSE;
}

std::wstring GetCurrentUserSid()
{
	HANDLE hToken = nullptr;
	if (!OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &hToken))
		return L"";

	DWORD length = 0;
	GetTokenInformation(hToken, TokenUser, nullptr, 0, &length);
	if (GetLastError() != ERROR_INSUFFICIENT_BUFFER)
	{
		CloseHandle(hToken);
		return L"";
	}

	std::vector<BYTE> buffer(length);
	if (!GetTokenInformation(hToken, TokenUser, buffer.data(), length, &length))
	{
		CloseHandle(hToken);
		return L"";
	}
	CloseHandle(hToken);

	TOKEN_USER* pUser = reinterpret_cast<TOKEN_USER*>(buffer.data());
	LPWSTR sidString = nullptr;
	if (!ConvertSidToStringSidW(pUser->User.Sid, &sidString))
	{
		return L"";
	}

	std::wstring result = sidString;
	LocalFree(sidString); // Free the memory allocated by ConvertSidToStringSid
	return result;
}